using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Text;
using System.IO;
using Newtonsoft.Json;

namespace TantraBotSystem
{
    /// <summary>
    /// High-performance C# implementation of Tantra Bot
    /// Superior to AutoIt with native Windows API access
    /// </summary>
    public class TantraBot
    {
        #region Windows API Imports
        
        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, uint dwProcessId);
        
        [DllImport("kernel32.dll")]
        public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint dwSize, out uint lpNumberOfBytesRead);
        
        [DllImport("kernel32.dll")]
        public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint dwSize, out uint lpNumberOfBytesWritten);
        
        [DllImport("kernel32.dll")]
        public static extern bool CloseHandle(IntPtr hObject);
        
        [DllImport("user32.dll")]
        public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);
        
        [DllImport("user32.dll")]
        public static extern bool SetForegroundWindow(IntPtr hWnd);
        
        [DllImport("user32.dll")]
        public static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);
        
        [DllImport("user32.dll")]
        public static extern bool PostMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);
        
        // Constants
        const uint PROCESS_ALL_ACCESS = 0x1F0FFF;
        const uint WM_KEYDOWN = 0x0100;
        const uint WM_KEYUP = 0x0101;
        const uint WM_CHAR = 0x0102;
        
        #endregion
        
        #region Configuration Classes
        
        public class BotConfig
        {
            public string WindowTitle { get; set; } = "Tantra";
            public bool AutoLoot { get; set; } = true;
            public int LootDuration { get; set; } = 1000;
            public bool AutoRepair { get; set; } = true;
            public bool AutoSkills { get; set; } = true;
            public bool AutoBuffs { get; set; } = true;
            public List<string> MonsterList { get; set; } = new List<string>();
            public Dictionary<string, int> SkillDelays { get; set; } = new Dictionary<string, int>
            {
                {"Skill1", 2000}, {"Skill2", 3000}, {"Skill3", 4000}, {"Skill4", 5000}
            };
            public Dictionary<string, int> BuffDelays { get; set; } = new Dictionary<string, int>
            {
                {"Buff1", 30000}, {"Buff2", 45000}, {"Buff3", 60000}, {"Buff4", 120000}
            };
            public bool AutoReplyPM { get; set; } = false;
            public string ReplyMessage { get; set; } = "AFK - Auto Reply";
            public bool AntiStuck { get; set; } = true;
        }
        
        public class MemoryAddresses
        {
            public IntPtr CharacterHP { get; set; } = (IntPtr)0x12345678;
            public IntPtr CharacterTP { get; set; } = (IntPtr)0x12345679;
            public IntPtr CharacterName { get; set; } = (IntPtr)0x1234567A;
            public IntPtr TargetName { get; set; } = (IntPtr)0x1234567B;
            public IntPtr TargetHP { get; set; } = (IntPtr)0x1234567C;
        }
        
        #endregion
        
        #region Core Classes
        
        public class MemoryManager
        {
            private IntPtr processHandle;
            private Process gameProcess;
            private MemoryAddresses addresses;
            
            public MemoryManager(MemoryAddresses addresses)
            {
                this.addresses = addresses;
            }
            
            public bool AttachToProcess(string processName)
            {
                try
                {
                    Process[] processes = Process.GetProcessesByName(processName);
                    if (processes.Length == 0)
                        return false;
                    
                    gameProcess = processes[0];
                    processHandle = OpenProcess(PROCESS_ALL_ACCESS, false, (uint)gameProcess.Id);
                    
                    return processHandle != IntPtr.Zero;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to attach to process: {ex.Message}");
                    return false;
                }
            }
            
            public int ReadInt32(IntPtr address)
            {
                byte[] buffer = new byte[4];
                uint bytesRead;
                
                if (ReadProcessMemory(processHandle, address, buffer, 4, out bytesRead))
                {
                    return BitConverter.ToInt32(buffer, 0);
                }
                return 0;
            }
            
            public string ReadString(IntPtr address, int maxLength = 50)
            {
                byte[] buffer = new byte[maxLength];
                uint bytesRead;
                
                if (ReadProcessMemory(processHandle, address, buffer, (uint)maxLength, out bytesRead))
                {
                    return Encoding.ASCII.GetString(buffer).TrimEnd('\0');
                }
                return string.Empty;
            }
            
            public bool WriteInt32(IntPtr address, int value)
            {
                byte[] buffer = BitConverter.GetBytes(value);
                uint bytesWritten;
                
                return WriteProcessMemory(processHandle, address, buffer, 4, out bytesWritten);
            }
            
            public void Dispose()
            {
                if (processHandle != IntPtr.Zero)
                {
                    CloseHandle(processHandle);
                    processHandle = IntPtr.Zero;
                }
            }
        }
        
        public class GameController
        {
            private BotConfig config;
            private MemoryManager memory;
            private MemoryAddresses addresses;
            private IntPtr gameWindow;
            private bool isRunning;
            private Dictionary<string, DateTime> lastSkillTimes;
            private Dictionary<string, DateTime> lastBuffTimes;
            private CancellationTokenSource cancellationToken;
            
            public GameController(BotConfig config, MemoryAddresses addresses)
            {
                this.config = config;
                this.addresses = addresses;
                this.memory = new MemoryManager(addresses);
                this.lastSkillTimes = new Dictionary<string, DateTime>();
                this.lastBuffTimes = new Dictionary<string, DateTime>();
            }
            
            public bool Initialize()
            {
                // Find game window
                gameWindow = FindWindow(null, config.WindowTitle);
                if (gameWindow == IntPtr.Zero)
                {
                    MessageBox.Show("Game window not found!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
                
                // Attach to process
                if (!memory.AttachToProcess("Tantra"))
                {
                    MessageBox.Show("Could not attach to game process!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
                
                return true;
            }
            
            public void StartBot()
            {
                if (isRunning) return;
                
                isRunning = true;
                cancellationToken = new CancellationTokenSource();
                
                Task.Run(() => MainBotLoop(cancellationToken.Token));
            }
            
            public void StopBot()
            {
                isRunning = false;
                cancellationToken?.Cancel();
            }
            
            private async Task MainBotLoop(CancellationToken token)
            {
                while (isRunning && !token.IsCancellationRequested)
                {
                    try
                    {
                        // Check if game window still exists
                        if (gameWindow == IntPtr.Zero || !IsWindowValid())
                        {
                            await Task.Delay(5000, token);
                            continue;
                        }
                        
                        // Core bot sequence
                        CheckHealth();
                        
                        // Use buffs
                        if (config.AutoBuffs)
                        {
                            UseBuffIfReady("Buff1", Keys.F2);
                            UseBuffIfReady("Buff2", Keys.F3);
                            UseBuffIfReady("Buff3", Keys.F4);
                            UseBuffIfReady("Buff4", Keys.F5);
                        }
                        
                        // Find and attack target
                        if (FindTarget())
                        {
                            if (config.AutoSkills)
                            {
                                UseSkillIfReady("Skill1", Keys.D1);
                                await Task.Delay(500, token);
                                UseSkillIfReady("Skill2", Keys.D2);
                                await Task.Delay(500, token);
                                UseSkillIfReady("Skill3", Keys.D3);
                                await Task.Delay(500, token);
                                UseSkillIfReady("Skill4", Keys.D4);
                            }
                        }
                        
                        // Auto loot
                        if (config.AutoLoot)
                        {
                            SendKey(Keys.F);
                            await Task.Delay(config.LootDuration, token);
                        }
                        
                        // Anti-stuck movement
                        if (config.AntiStuck && DateTime.Now.Second % 30 == 0)
                        {
                            PerformAntiStuckMovement();
                        }
                        
                        await Task.Delay(100, token); // Prevent excessive CPU usage
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error in main loop: {ex.Message}");
                        await Task.Delay(1000, token);
                    }
                }
            }
            
            private bool IsWindowValid()
            {
                return gameWindow != IntPtr.Zero;
            }
            
            private void CheckHealth()
            {
                int currentHP = memory.ReadInt32(addresses.CharacterHP);
                int maxHP = memory.ReadInt32(addresses.CharacterHP + 4); // Assuming max HP is 4 bytes after current HP
                
                if (maxHP > 0 && currentHP < maxHP * 0.3) // Less than 30% health
                {
                    SendKey(Keys.D1); // Use healing potion
                }
            }
            
            private bool FindTarget()
            {
                SendKey(Keys.T); // Target nearest enemy
                Thread.Sleep(500);
                
                if (config.MonsterList.Count == 0)
                    return true; // Accept any target if no filter
                
                string targetName = memory.ReadString(addresses.TargetName);
                
                foreach (string monster in config.MonsterList)
                {
                    if (targetName.ToLower().Contains(monster.ToLower()))
                    {
                        return true;
                    }
                }
                
                // Target not in list, try to find another
                SendKey(Keys.A); // Turn to find monsters
                Thread.Sleep(300);
                return false;
            }
            
            private void UseSkillIfReady(string skillName, Keys key)
            {
                if (!lastSkillTimes.ContainsKey(skillName))
                    lastSkillTimes[skillName] = DateTime.MinValue;
                
                int cooldown = config.SkillDelays.ContainsKey(skillName) ? config.SkillDelays[skillName] : 2000;
                
                if ((DateTime.Now - lastSkillTimes[skillName]).TotalMilliseconds >= cooldown)
                {
                    SendKey(key);
                    lastSkillTimes[skillName] = DateTime.Now;
                }
            }
            
            private void UseBuffIfReady(string buffName, Keys key)
            {
                if (!lastBuffTimes.ContainsKey(buffName))
                    lastBuffTimes[buffName] = DateTime.MinValue;
                
                int cooldown = config.BuffDelays.ContainsKey(buffName) ? config.BuffDelays[buffName] : 30000;
                
                if ((DateTime.Now - lastBuffTimes[buffName]).TotalMilliseconds >= cooldown)
                {
                    SendKey(key);
                    lastBuffTimes[buffName] = DateTime.Now;
                }
            }
            
            private void PerformAntiStuckMovement()
            {
                SendKey(Keys.A); // Turn
                Thread.Sleep(300);
                SendKey(Keys.W); // Move forward
                Thread.Sleep(500);
            }
            
            private void SendKey(Keys key)
            {
                try
                {
                    SetForegroundWindow(gameWindow);
                    Thread.Sleep(50);
                    
                    // Send key down
                    PostMessage(gameWindow, WM_KEYDOWN, (IntPtr)key, IntPtr.Zero);
                    Thread.Sleep(50);
                    
                    // Send key up
                    PostMessage(gameWindow, WM_KEYUP, (IntPtr)key, IntPtr.Zero);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to send key {key}: {ex.Message}");
                }
            }
            
            public void Dispose()
            {
                StopBot();
                memory?.Dispose();
            }
        }
        
        #endregion
        
        #region Main Application
        
        public partial class MainForm : Form
        {
            private BotConfig config;
            private MemoryAddresses addresses;
            private GameController controller;
            private System.Windows.Forms.Timer statusTimer;
            
            // UI Controls
            private TabControl tabControl;
            private Button startButton, stopButton;
            private CheckBox autoLootCheck, autoSkillsCheck, autoBuffsCheck;
            private TextBox monsterListText, windowTitleText;
            private Label statusLabel;
            
            public MainForm()
            {
                InitializeComponent();
                config = new BotConfig();
                addresses = new MemoryAddresses();
                controller = new GameController(config, addresses);
                
                SetupStatusTimer();
                RegisterGlobalHotkeys();
            }
            
            private void InitializeComponent()
            {
                this.Text = "Tantra Bot System - C# Edition v2.0";
                this.Size = new System.Drawing.Size(600, 800);
                this.StartPosition = FormStartPosition.CenterScreen;
                
                // Create tab control
                tabControl = new TabControl();
                tabControl.Dock = DockStyle.Fill;
                this.Controls.Add(tabControl);
                
                CreateMainTab();
                CreateSkillsTab();
                CreateSettingsTab();
                
                // Status bar
                statusLabel = new Label();
                statusLabel.Text = "Ready";
                statusLabel.Dock = DockStyle.Bottom;
                statusLabel.BorderStyle = BorderStyle.Fixed3D;
                this.Controls.Add(statusLabel);
            }
            
            private void CreateMainTab()
            {
                TabPage mainTab = new TabPage("Main Controls");
                tabControl.TabPages.Add(mainTab);
                
                // Start/Stop buttons
                Panel buttonPanel = new Panel();
                buttonPanel.Height = 50;
                buttonPanel.Dock = DockStyle.Top;
                mainTab.Controls.Add(buttonPanel);
                
                startButton = new Button();
                startButton.Text = "Start Bot";
                startButton.Size = new System.Drawing.Size(100, 30);
                startButton.Location = new System.Drawing.Point(10, 10);
                startButton.Click += StartButton_Click;
                buttonPanel.Controls.Add(startButton);
                
                stopButton = new Button();
                stopButton.Text = "Stop Bot";
                stopButton.Size = new System.Drawing.Size(100, 30);
                stopButton.Location = new System.Drawing.Point(120, 10);
                stopButton.Enabled = false;
                stopButton.Click += StopButton_Click;
                buttonPanel.Controls.Add(stopButton);
                
                // Auto features
                GroupBox featuresGroup = new GroupBox();
                featuresGroup.Text = "Auto Features";
                featuresGroup.Height = 100;
                featuresGroup.Dock = DockStyle.Top;
                mainTab.Controls.Add(featuresGroup);
                
                autoLootCheck = new CheckBox();
                autoLootCheck.Text = "Auto Loot";
                autoLootCheck.Location = new System.Drawing.Point(10, 20);
                autoLootCheck.Checked = config.AutoLoot;
                featuresGroup.Controls.Add(autoLootCheck);
                
                autoSkillsCheck = new CheckBox();
                autoSkillsCheck.Text = "Auto Skills";
                autoSkillsCheck.Location = new System.Drawing.Point(10, 45);
                autoSkillsCheck.Checked = config.AutoSkills;
                featuresGroup.Controls.Add(autoSkillsCheck);
                
                autoBuffsCheck = new CheckBox();
                autoBuffsCheck.Text = "Auto Buffs";
                autoBuffsCheck.Location = new System.Drawing.Point(10, 70);
                autoBuffsCheck.Checked = config.AutoBuffs;
                featuresGroup.Controls.Add(autoBuffsCheck);
                
                // Monster list
                GroupBox monsterGroup = new GroupBox();
                monsterGroup.Text = "Target Monsters";
                monsterGroup.Dock = DockStyle.Fill;
                mainTab.Controls.Add(monsterGroup);
                
                monsterListText = new TextBox();
                monsterListText.Multiline = true;
                monsterListText.ScrollBars = ScrollBars.Vertical;
                monsterListText.Dock = DockStyle.Fill;
                monsterListText.Text = "Orc\nGoblin\nSkeleton\nWolf";
                monsterGroup.Controls.Add(monsterListText);
            }
            
            private void CreateSkillsTab()
            {
                TabPage skillsTab = new TabPage("Skills & Buffs");
                tabControl.TabPages.Add(skillsTab);
                
                // Add skill and buff configuration controls here
                Label skillsLabel = new Label();
                skillsLabel.Text = "Skill and buff configuration will be implemented here";
                skillsLabel.Dock = DockStyle.Fill;
                skillsTab.Controls.Add(skillsLabel);
            }
            
            private void CreateSettingsTab()
            {
                TabPage settingsTab = new TabPage("Settings");
                tabControl.TabPages.Add(settingsTab);
                
                // Window title
                GroupBox windowGroup = new GroupBox();
                windowGroup.Text = "Game Window";
                windowGroup.Height = 80;
                windowGroup.Dock = DockStyle.Top;
                settingsTab.Controls.Add(windowGroup);
                
                Label titleLabel = new Label();
                titleLabel.Text = "Window Title:";
                titleLabel.Location = new System.Drawing.Point(10, 20);
                windowGroup.Controls.Add(titleLabel);
                
                windowTitleText = new TextBox();
                windowTitleText.Text = config.WindowTitle;
                windowTitleText.Location = new System.Drawing.Point(10, 40);
                windowTitleText.Width = 200;
                windowGroup.Controls.Add(windowTitleText);
                
                // File operations
                GroupBox fileGroup = new GroupBox();
                fileGroup.Text = "Configuration";
                fileGroup.Height = 80;
                fileGroup.Dock = DockStyle.Top;
                settingsTab.Controls.Add(fileGroup);
                
                Button saveButton = new Button();
                saveButton.Text = "Save Config";
                saveButton.Location = new System.Drawing.Point(10, 20);
                saveButton.Click += SaveButton_Click;
                fileGroup.Controls.Add(saveButton);
                
                Button loadButton = new Button();
                loadButton.Text = "Load Config";
                loadButton.Location = new System.Drawing.Point(120, 20);
                loadButton.Click += LoadButton_Click;
                fileGroup.Controls.Add(loadButton);
            }
            
            private void SetupStatusTimer()
            {
                statusTimer = new System.Windows.Forms.Timer();
                statusTimer.Interval = 1000; // Update every second
                statusTimer.Tick += StatusTimer_Tick;
                statusTimer.Start();
            }
            
            private void RegisterGlobalHotkeys()
            {
                // Register F10 for emergency stop
                // Implementation would require additional P/Invoke for global hotkeys
            }
            
            private void StartButton_Click(object sender, EventArgs e)
            {
                UpdateConfigFromUI();
                
                if (controller.Initialize())
                {
                    controller.StartBot();
                    startButton.Enabled = false;
                    stopButton.Enabled = true;
                    statusLabel.Text = "Bot Running...";
                }
                else
                {
                    MessageBox.Show("Failed to start bot!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            
            private void StopButton_Click(object sender, EventArgs e)
            {
                controller.StopBot();
                startButton.Enabled = true;
                stopButton.Enabled = false;
                statusLabel.Text = "Bot Stopped";
            }
            
            private void SaveButton_Click(object sender, EventArgs e)
            {
                UpdateConfigFromUI();
                
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*";
                saveDialog.DefaultExt = "json";
                
                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        string json = JsonConvert.SerializeObject(config, Formatting.Indented);
                        File.WriteAllText(saveDialog.FileName, json);
                        MessageBox.Show("Configuration saved!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to save config: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            
            private void LoadButton_Click(object sender, EventArgs e)
            {
                OpenFileDialog openDialog = new OpenFileDialog();
                openDialog.Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*";
                
                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        string json = File.ReadAllText(openDialog.FileName);
                        config = JsonConvert.DeserializeObject<BotConfig>(json);
                        UpdateUIFromConfig();
                        MessageBox.Show("Configuration loaded!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to load config: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            
            private void StatusTimer_Tick(object sender, EventArgs e)
            {
                // Update status information
                if (controller != null)
                {
                    // Add real-time status updates here
                }
            }
            
            private void UpdateConfigFromUI()
            {
                config.AutoLoot = autoLootCheck.Checked;
                config.AutoSkills = autoSkillsCheck.Checked;
                config.AutoBuffs = autoBuffsCheck.Checked;
                config.WindowTitle = windowTitleText.Text;
                
                // Update monster list
                config.MonsterList.Clear();
                string[] monsters = monsterListText.Text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string monster in monsters)
                {
                    if (!string.IsNullOrWhiteSpace(monster))
                        config.MonsterList.Add(monster.Trim());
                }
            }
            
            private void UpdateUIFromConfig()
            {
                autoLootCheck.Checked = config.AutoLoot;
                autoSkillsCheck.Checked = config.AutoSkills;
                autoBuffsCheck.Checked = config.AutoBuffs;
                windowTitleText.Text = config.WindowTitle;
                monsterListText.Text = string.Join("\r\n", config.MonsterList);
            }
            
            protected override void OnFormClosing(FormClosingEventArgs e)
            {
                controller?.Dispose();
                base.OnFormClosing(e);
            }
        }
        
        #endregion
        
        #region Program Entry Point
        
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new MainForm());
        }
        
        #endregion
    }
}
