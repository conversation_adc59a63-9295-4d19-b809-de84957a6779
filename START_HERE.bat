@echo off
title Kathana Bot - Complete Auto Setup
color 0A
cls

echo.
echo  ██╗  ██╗ █████╗ ████████╗██╗  ██╗ █████╗ ███╗   ██╗ █████╗
echo  ██║ ██╔╝██╔══██╗╚══██╔══╝██║  ██║██╔══██╗████╗  ██║██╔══██╗
echo  █████╔╝ ███████║   ██║   ███████║███████║██╔██╗ ██║███████║
echo  ██╔═██╗ ██╔══██║   ██║   ██╔══██║██╔══██║██║╚██╗██║██╔══██║
echo  ██║  ██╗██║  ██║   ██║   ██║  ██║██║  ██║██║ ╚████║██║  ██║
echo  ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝
echo.
echo                    🎮 BOT SYSTEM 🎮
echo                 COMPLETE AUTO SETUP
echo.
echo  ================================================================
echo   This will AUTOMATICALLY do EVERYTHING for you:
echo   ✅ Install Python if needed
echo   ✅ Install all required packages  
echo   ✅ Find memory addresses automatically
echo   ✅ Set up the bot completely
echo   ✅ Create easy launchers
echo.
echo   NO TECHNICAL KNOWLEDGE REQUIRED!
echo   Just follow the simple prompts!
echo  ================================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Python not found - will be installed automatically
) else (
    echo ✅ Python detected
    python --version
)

echo.
echo 🚀 Ready to start the magic?
echo.
echo Press any key to begin COMPLETE AUTO SETUP...
pause >nul

echo.
echo 🪄 Starting the magic...
echo.

REM Run the master launcher
python MASTER_LAUNCHER.py

echo.
echo 🎉 Setup process completed!
echo.
echo If everything worked, you should now have:
echo ✅ Working Python installation
echo ✅ All packages installed
echo ✅ Memory addresses found
echo ✅ Bot ready to use
echo.
echo 🚀 To start botting, run: LAUNCH_BOT.bat
echo.
echo Press any key to exit...
pause >nul
