# 🎮 Tantra Bot System - Python Edition

A modern, powerful rewrite of the AutoIt Tantra bot using Python with enhanced features, better performance, and a sleek GUI.

## 🚀 **SUPER EASY SETUP - NO TECHNICAL KNOWLEDGE NEEDED!**

### **🎯 ONE-CLICK SETUP (Recommended)**

**Just double-click `START_HERE.bat` and everything happens automatically!**

This will:
- ✅ Install Python if needed
- ✅ Install all packages automatically
- ✅ Find memory addresses automatically
- ✅ Set up the bot completely
- ✅ Create easy launchers

**That's it! No manual work required!**

### **🎮 Alternative Setup Methods**

#### **Method 1: Master Launcher (Python)**
```bash
python MASTER_LAUNCHER.py
```

#### **Method 2: Step by Step**
```bash
# 1. Install everything
python auto_setup.py

# 2. Find addresses automatically
python auto_address_finder.py

# 3. Launch bot
python tantra_bot.py
```

#### **Method 3: Manual (Advanced Users)**
```bash
# 1. Install packages
pip install -r requirements.txt

# 2. Find addresses manually
python memory_scanner.py

# 3. Launch bot
python tantra_bot.py
```

## ✨ Features

### 🤖 Core Automation
- **Smart Target Selection** - Configurable monster targeting with name filtering
- **Skill Rotation** - Automated skill usage with customizable delays and cooldowns
- **Buff Management** - Intelligent buff maintenance with timing optimization
- **Auto Looting** - Efficient item collection with configurable duration
- **Health Management** - Automatic healing and potion usage
- **Anti-Stuck System** - Smart movement when character gets stuck

### 🎯 Advanced Features
- **Automatic Memory Address Finding** - No more manual address hunting!
- **Real-time Memory Reading** - Live character stats and game state monitoring
- **Window Management** - Robust game window detection and interaction
- **Configuration System** - Save/load settings with JSON format
- **Global Hotkeys** - F10 emergency stop, Shift+Alt+M to show GUI
- **Multi-threading** - Responsive GUI while bot runs in background
- **Comprehensive Logging** - Detailed activity logs for debugging

### 🎨 Modern GUI
- **Tabbed Interface** - Organized controls across multiple tabs
- **Real-time Status** - Live bot status and character information
- **Easy Configuration** - Intuitive settings for all bot features
- **Professional Design** - Clean, modern interface using tkinter

## 🚀 Quick Start

### **🎯 EASIEST WAY (30 seconds):**
1. **Double-click `START_HERE.bat`**
2. **Follow the prompts**
3. **Start Tantra and log in**
4. **Bot finds addresses automatically**
5. **Start botting!**

### Prerequisites
- Windows OS (for game compatibility)
- Tantra Online game client
- Python 3.8+ (auto-installed if needed)

### First Time Setup

1. **Configure Game Window:**
   - Go to Settings tab
   - Set the correct window title for your Tantra client
   - Default is "Tantra" but may need adjustment

2. **Set Target Monsters:**
   - In Main Controls tab, add monster names to target
   - One monster name per line
   - Case-insensitive matching

3. **Adjust Skill Timings:**
   - Go to Skills & Buffs tab
   - Set appropriate delays for your character's skills
   - Values are in milliseconds

4. **Start the Bot:**
   - Click "Start Bot" in Main Controls
   - Bot will automatically find game window and begin automation

## ⚙️ Configuration

### Monster Targeting
Add monster names in the "Target Monsters" text area:
```
Orc Warrior
Goblin Shaman
Skeleton Archer
Fire Wolf
```

### Skill Delays
Adjust timing for each skill (in milliseconds):
- **Skill1**: 2000ms (2 seconds)
- **Skill2**: 3000ms (3 seconds)
- **Skill3**: 4000ms (4 seconds)
- **Skill4**: 5000ms (5 seconds)

### Buff Delays
Set cooldown times for buffs:
- **Buff1**: 30000ms (30 seconds)
- **Buff2**: 45000ms (45 seconds)
- **Buff3**: 60000ms (1 minute)
- **Buff4**: 120000ms (2 minutes)

## 🎮 Hotkeys

- **F10** - Emergency stop bot
- **Shift+Alt+M** - Show/hide bot GUI
- **Start/Stop** - Use GUI buttons for normal operation

## 📁 File Structure

```
tantra_bot/
├── tantra_bot.py      # Main bot application
├── requirements.txt   # Python dependencies
├── README.md         # This file
├── config.json       # Saved configuration (created after first save)
└── logs/             # Bot activity logs (auto-created)
```

## 🔧 Advanced Configuration

### Memory Addresses
The bot uses placeholder memory addresses that need to be updated for your specific game version:

```python
# In MemoryManager class, update these addresses:
hp_address = 0x12345678      # Character HP address
tp_address = 0x12345679      # Character TP address  
target_name_address = 0x1234567A  # Target name address
```

### Finding Memory Addresses
Use tools like:
- **Cheat Engine** - Popular memory scanner
- **Process Hacker** - Advanced process analysis
- **x64dbg** - Debugger for reverse engineering

### Custom Skills
Modify the skill system in `GameController.use_skill()`:

```python
def use_skill(self, skill_key: str, skill_name: str):
    # Add custom skill logic here
    if skill_name == "special_attack":
        self.send_key('ctrl+1')  # Custom key combination
    else:
        self.send_key(skill_key)  # Normal skill
```

## 🛡️ Safety Features

- **Process Monitoring** - Automatically detects if game closes
- **Error Handling** - Graceful recovery from exceptions
- **Emergency Stop** - Instant bot termination with F10
- **Window Validation** - Ensures commands only sent to game window
- **Threading Safety** - Prevents GUI freezing during automation

## 🐛 Troubleshooting

### Bot Won't Start
1. Check if Tantra game is running
2. Verify window title in Settings tab
3. Run as Administrator if needed
4. Check Python dependencies are installed

### Skills Not Working
1. Verify game window has focus
2. Check skill key bindings in game
3. Adjust skill delays if too fast
4. Ensure character has enough TP/MP

### Memory Reading Issues
1. Update memory addresses for your game version
2. Run bot as Administrator
3. Disable antivirus temporarily
4. Check if game has anti-cheat protection

### Performance Issues
1. Reduce skill/buff check frequency
2. Close unnecessary programs
3. Lower Windows visual effects
4. Use SSD for better I/O performance

## 🔄 Upgrading from AutoIt Version

### Key Improvements
- **Better Performance** - Native Python execution vs interpreted AutoIt
- **Enhanced GUI** - Modern tabbed interface vs basic windows
- **Robust Error Handling** - Graceful recovery vs crashes
- **Flexible Configuration** - JSON format vs custom cfg files
- **Advanced Logging** - Detailed activity tracking
- **Cross-Platform Ready** - Easy to port to Linux/Mac

### Migration Steps
1. Export your AutoIt configuration
2. Manually transfer settings to new Python bot
3. Update memory addresses if needed
4. Test thoroughly before extended use

## 📈 Future Enhancements

- **Image Recognition** - OCR for reading game text
- **Machine Learning** - Adaptive behavior patterns
- **Web Interface** - Remote bot control via browser
- **Plugin System** - Modular feature extensions
- **Multi-Character Support** - Manage multiple game instances
- **Advanced Pathfinding** - Intelligent movement algorithms

## ⚠️ Disclaimer

This bot is for educational purposes only. Use at your own risk and in accordance with the game's terms of service. The developers are not responsible for any account penalties or bans resulting from bot usage.

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Happy Botting! 🎮✨**
