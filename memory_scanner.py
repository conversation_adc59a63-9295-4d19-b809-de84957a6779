#!/usr/bin/env python3
"""
Tantra Memory Scanner - Find and Update Memory Addresses
This tool helps you find the correct memory addresses for your game version
"""

import pymem
import pymem.process
import struct
import time
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import json
from typing import List, Dict, Tuple, Optional
import re

class MemoryScanner:
    """Advanced memory scanner for finding game addresses"""
    
    def __init__(self):
        self.process = None
        self.base_address = None
        self.scan_results = []
        
    def attach_to_process(self, process_name: str = "Tantra") -> bool:
        """Attach to the game process"""
        try:
            # Try different common process names
            possible_names = [process_name, "Tantra.exe", "tantra", "client"]
            
            for name in possible_names:
                try:
                    self.process = pymem.Pymem(name)
                    self.base_address = self.process.base_address
                    print(f"✅ Attached to {name} (PID: {self.process.process_id})")
                    print(f"📍 Base Address: {hex(self.base_address)}")
                    return True
                except:
                    continue
                    
            print("❌ Could not find <PERSON>tra process. Make sure the game is running!")
            return False
            
        except Exception as e:
            print(f"❌ Failed to attach: {e}")
            return False
    
    def scan_for_value(self, value: int, value_type: str = "int") -> List[int]:
        """Scan memory for a specific value"""
        addresses = []
        
        if not self.process:
            return addresses
            
        try:
            # Get memory regions
            for region in self.process.memory_regions():
                if region.State != 0x1000:  # MEM_COMMIT
                    continue
                if region.Protect not in [0x04, 0x20, 0x40]:  # Readable regions
                    continue
                    
                try:
                    # Read memory region
                    data = self.process.read_bytes(region.BaseAddress, region.RegionSize)
                    
                    # Search for value
                    if value_type == "int":
                        pattern = struct.pack('<I', value)  # Little-endian 32-bit int
                        step = 4
                    elif value_type == "short":
                        pattern = struct.pack('<H', value)  # Little-endian 16-bit int
                        step = 2
                    elif value_type == "byte":
                        pattern = struct.pack('B', value)   # 8-bit byte
                        step = 1
                    else:
                        continue
                    
                    # Find all occurrences
                    offset = 0
                    while True:
                        pos = data.find(pattern, offset)
                        if pos == -1:
                            break
                        
                        address = region.BaseAddress + pos
                        addresses.append(address)
                        offset = pos + step
                        
                        # Limit results to prevent memory issues
                        if len(addresses) > 10000:
                            break
                            
                except:
                    continue
                    
        except Exception as e:
            print(f"❌ Scan error: {e}")
            
        return addresses
    
    def scan_for_string(self, text: str, encoding: str = "ascii") -> List[int]:
        """Scan memory for a text string"""
        addresses = []
        
        if not self.process:
            return addresses
            
        try:
            # Convert string to bytes
            if encoding == "ascii":
                pattern = text.encode('ascii') + b'\x00'  # Null-terminated
            elif encoding == "utf8":
                pattern = text.encode('utf-8') + b'\x00'
            elif encoding == "utf16":
                pattern = text.encode('utf-16le') + b'\x00\x00'
            else:
                pattern = text.encode('ascii')
            
            # Search in memory regions
            for region in self.process.memory_regions():
                if region.State != 0x1000:  # MEM_COMMIT
                    continue
                if region.Protect not in [0x04, 0x20, 0x40]:  # Readable regions
                    continue
                    
                try:
                    data = self.process.read_bytes(region.BaseAddress, region.RegionSize)
                    
                    offset = 0
                    while True:
                        pos = data.find(pattern, offset)
                        if pos == -1:
                            break
                        
                        address = region.BaseAddress + pos
                        addresses.append(address)
                        offset = pos + 1
                        
                        if len(addresses) > 1000:
                            break
                            
                except:
                    continue
                    
        except Exception as e:
            print(f"❌ String scan error: {e}")
            
        return addresses
    
    def read_value(self, address: int, value_type: str = "int"):
        """Read value from memory address"""
        try:
            if value_type == "int":
                return self.process.read_int(address)
            elif value_type == "short":
                return self.process.read_short(address)
            elif value_type == "byte":
                return self.process.read_uchar(address)
            elif value_type == "float":
                return self.process.read_float(address)
            elif value_type == "string":
                return self.process.read_string(address, 50).strip('\x00')
            else:
                return None
        except:
            return None
    
    def filter_addresses_by_range(self, addresses: List[int], min_addr: int = None, max_addr: int = None) -> List[int]:
        """Filter addresses by range"""
        filtered = []
        
        for addr in addresses:
            if min_addr and addr < min_addr:
                continue
            if max_addr and addr > max_addr:
                continue
            filtered.append(addr)
            
        return filtered
    
    def find_pointer_chain(self, target_address: int, max_depth: int = 4) -> List[List[int]]:
        """Find pointer chains leading to target address"""
        chains = []
        
        # This is a simplified pointer chain finder
        # In practice, this would be much more complex
        
        return chains

class AddressFinder:
    """Helper class to find specific game addresses"""
    
    def __init__(self, scanner: MemoryScanner):
        self.scanner = scanner
        
    def find_character_hp(self) -> Optional[int]:
        """Find character HP address by scanning for current HP value"""
        print("🔍 Finding Character HP Address...")
        print("📋 Instructions:")
        print("1. Note your current HP in game")
        print("2. Enter that value below")
        print("3. Take some damage or heal")
        print("4. Enter the new HP value")
        
        return self._find_changing_value("Character HP")
    
    def find_character_name(self) -> Optional[int]:
        """Find character name address"""
        print("🔍 Finding Character Name Address...")
        char_name = input("Enter your character name: ").strip()
        
        if not char_name:
            return None
            
        addresses = self.scanner.scan_for_string(char_name, "ascii")
        
        if addresses:
            print(f"✅ Found {len(addresses)} potential addresses for '{char_name}':")
            for i, addr in enumerate(addresses[:10]):  # Show first 10
                print(f"  {i+1}. {hex(addr)}")
            return addresses[0]
        else:
            print(f"❌ Could not find '{char_name}' in memory")
            return None
    
    def find_target_name(self) -> Optional[int]:
        """Find target name address"""
        print("🔍 Finding Target Name Address...")
        print("📋 Instructions:")
        print("1. Target a monster in game")
        print("2. Enter the monster's name below")
        
        target_name = input("Enter target monster name: ").strip()
        
        if not target_name:
            return None
            
        addresses = self.scanner.scan_for_string(target_name, "ascii")
        
        if addresses:
            print(f"✅ Found {len(addresses)} potential addresses for '{target_name}':")
            for i, addr in enumerate(addresses[:10]):
                print(f"  {i+1}. {hex(addr)}")
            return addresses[0]
        else:
            print(f"❌ Could not find '{target_name}' in memory")
            return None
    
    def _find_changing_value(self, value_name: str) -> Optional[int]:
        """Find address by scanning for changing values"""
        try:
            # First scan
            first_value = int(input(f"Enter current {value_name}: "))
            print(f"🔍 Scanning for value {first_value}...")
            
            first_addresses = self.scanner.scan_for_value(first_value, "int")
            print(f"📊 Found {len(first_addresses)} addresses with value {first_value}")
            
            if len(first_addresses) > 10000:
                print("⚠️ Too many results. Try a more specific value.")
                return None
            
            # Wait for value to change
            input(f"Change your {value_name} in game, then press Enter...")
            
            # Second scan
            second_value = int(input(f"Enter new {value_name}: "))
            print(f"🔍 Filtering addresses with new value {second_value}...")
            
            valid_addresses = []
            for addr in first_addresses:
                current_value = self.scanner.read_value(addr, "int")
                if current_value == second_value:
                    valid_addresses.append(addr)
            
            print(f"✅ Found {len(valid_addresses)} matching addresses:")
            for i, addr in enumerate(valid_addresses[:10]):
                print(f"  {i+1}. {hex(addr)} = {self.scanner.read_value(addr, 'int')}")
            
            if valid_addresses:
                return valid_addresses[0]
            else:
                print("❌ No matching addresses found")
                return None
                
        except ValueError:
            print("❌ Invalid input. Please enter numbers only.")
            return None
        except Exception as e:
            print(f"❌ Error: {e}")
            return None

class MemoryScannerGUI:
    """GUI for the memory scanner"""
    
    def __init__(self):
        self.scanner = MemoryScanner()
        self.finder = AddressFinder(self.scanner)
        self.addresses = {}
        
        self.setup_gui()
        
    def setup_gui(self):
        """Create the GUI"""
        self.root = tk.Tk()
        self.root.title("Tantra Memory Scanner & Address Finder")
        self.root.geometry("800x600")
        
        # Create notebook
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Scanner tab
        scanner_frame = ttk.Frame(notebook)
        notebook.add(scanner_frame, text="Memory Scanner")
        self.create_scanner_tab(scanner_frame)
        
        # Address finder tab
        finder_frame = ttk.Frame(notebook)
        notebook.add(finder_frame, text="Address Finder")
        self.create_finder_tab(finder_frame)
        
        # Results tab
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="Found Addresses")
        self.create_results_tab(results_frame)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Attach to Tantra process first")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief='sunken')
        status_bar.pack(side='bottom', fill='x')
        
    def create_scanner_tab(self, parent):
        """Create memory scanner tab"""
        # Process connection
        process_frame = ttk.LabelFrame(parent, text="Process Connection")
        process_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(process_frame, text="Attach to Tantra", 
                  command=self.attach_process).pack(side='left', padx=5, pady=5)
        
        self.process_status = ttk.Label(process_frame, text="Not Connected")
        self.process_status.pack(side='left', padx=10)
        
        # Value scanner
        scan_frame = ttk.LabelFrame(parent, text="Scan for Value")
        scan_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(scan_frame, text="Value:").grid(row=0, column=0, padx=5, pady=5)
        self.scan_value_entry = ttk.Entry(scan_frame)
        self.scan_value_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(scan_frame, text="Type:").grid(row=0, column=2, padx=5, pady=5)
        self.scan_type_combo = ttk.Combobox(scan_frame, values=["int", "short", "byte", "float"])
        self.scan_type_combo.set("int")
        self.scan_type_combo.grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Button(scan_frame, text="Scan", 
                  command=self.scan_for_value).grid(row=0, column=4, padx=5, pady=5)
        
        # String scanner
        string_frame = ttk.LabelFrame(parent, text="Scan for String")
        string_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(string_frame, text="Text:").grid(row=0, column=0, padx=5, pady=5)
        self.scan_string_entry = ttk.Entry(string_frame)
        self.scan_string_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Button(string_frame, text="Scan", 
                  command=self.scan_for_string).grid(row=0, column=2, padx=5, pady=5)
        
        # Results display
        results_frame = ttk.LabelFrame(parent, text="Scan Results")
        results_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=15)
        self.results_text.pack(fill='both', expand=True, padx=5, pady=5)
        
    def create_finder_tab(self, parent):
        """Create address finder tab"""
        # Instructions
        instructions = ttk.LabelFrame(parent, text="Instructions")
        instructions.pack(fill='x', padx=5, pady=5)
        
        instruction_text = """
1. Make sure Tantra is running and you're logged in
2. Attach to the process in the Scanner tab
3. Use the buttons below to find specific addresses
4. Follow the prompts for each address type
5. Save the results when done
        """
        ttk.Label(instructions, text=instruction_text, justify='left').pack(padx=10, pady=5)
        
        # Address finders
        finders_frame = ttk.LabelFrame(parent, text="Find Addresses")
        finders_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(finders_frame, text="Find Character HP", 
                  command=self.find_character_hp).pack(pady=5)
        ttk.Button(finders_frame, text="Find Character TP/MP", 
                  command=self.find_character_tp).pack(pady=5)
        ttk.Button(finders_frame, text="Find Character Name", 
                  command=self.find_character_name).pack(pady=5)
        ttk.Button(finders_frame, text="Find Target Name", 
                  command=self.find_target_name).pack(pady=5)
        
        # Manual address entry
        manual_frame = ttk.LabelFrame(parent, text="Manual Address Entry")
        manual_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(manual_frame, text="Address Name:").grid(row=0, column=0, padx=5, pady=5)
        self.manual_name_entry = ttk.Entry(manual_frame)
        self.manual_name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(manual_frame, text="Address (hex):").grid(row=0, column=2, padx=5, pady=5)
        self.manual_addr_entry = ttk.Entry(manual_frame)
        self.manual_addr_entry.grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Button(manual_frame, text="Add", 
                  command=self.add_manual_address).grid(row=0, column=4, padx=5, pady=5)
        
    def create_results_tab(self, parent):
        """Create results tab"""
        # Found addresses
        addresses_frame = ttk.LabelFrame(parent, text="Found Addresses")
        addresses_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Treeview for addresses
        columns = ("Name", "Address", "Current Value", "Type")
        self.addresses_tree = ttk.Treeview(addresses_frame, columns=columns, show='headings')
        
        for col in columns:
            self.addresses_tree.heading(col, text=col)
            self.addresses_tree.column(col, width=150)
        
        self.addresses_tree.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(addresses_frame)
        button_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(button_frame, text="Refresh Values", 
                  command=self.refresh_values).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Export to JSON", 
                  command=self.export_addresses).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Generate Bot Code", 
                  command=self.generate_bot_code).pack(side='left', padx=5)
        
    def attach_process(self):
        """Attach to Tantra process"""
        if self.scanner.attach_to_process():
            self.process_status.config(text="✅ Connected")
            self.status_var.set("Connected to Tantra process")
        else:
            self.process_status.config(text="❌ Failed")
            self.status_var.set("Failed to connect - Make sure Tantra is running")
    
    def scan_for_value(self):
        """Scan for a specific value"""
        if not self.scanner.process:
            messagebox.showerror("Error", "Please attach to process first")
            return
        
        try:
            value = int(self.scan_value_entry.get())
            value_type = self.scan_type_combo.get()
            
            self.status_var.set(f"Scanning for {value}...")
            self.root.update()
            
            addresses = self.scanner.scan_for_value(value, value_type)
            
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"Found {len(addresses)} addresses for value {value}:\n\n")
            
            for i, addr in enumerate(addresses[:100]):  # Show first 100
                current_val = self.scanner.read_value(addr, value_type)
                self.results_text.insert(tk.END, f"{i+1:3d}. {hex(addr)} = {current_val}\n")
            
            if len(addresses) > 100:
                self.results_text.insert(tk.END, f"\n... and {len(addresses) - 100} more addresses")
            
            self.status_var.set(f"Scan complete - Found {len(addresses)} addresses")
            
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")
        except Exception as e:
            messagebox.showerror("Error", f"Scan failed: {e}")
    
    def scan_for_string(self):
        """Scan for a text string"""
        if not self.scanner.process:
            messagebox.showerror("Error", "Please attach to process first")
            return
        
        text = self.scan_string_entry.get().strip()
        if not text:
            messagebox.showerror("Error", "Please enter text to search for")
            return
        
        self.status_var.set(f"Scanning for '{text}'...")
        self.root.update()
        
        addresses = self.scanner.scan_for_string(text)
        
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"Found {len(addresses)} addresses for '{text}':\n\n")
        
        for i, addr in enumerate(addresses[:50]):  # Show first 50
            self.results_text.insert(tk.END, f"{i+1:3d}. {hex(addr)}\n")
        
        if len(addresses) > 50:
            self.results_text.insert(tk.END, f"\n... and {len(addresses) - 50} more addresses")
        
        self.status_var.set(f"String scan complete - Found {len(addresses)} addresses")
    
    def find_character_hp(self):
        """Find character HP address using guided process"""
        if not self.scanner.process:
            messagebox.showerror("Error", "Please attach to process first")
            return
        
        # Create dialog for HP finding
        dialog = HPFinderDialog(self.root, self.scanner)
        self.root.wait_window(dialog.dialog)
        
        if dialog.result:
            self.addresses["character_hp"] = dialog.result
            self.update_addresses_display()
            messagebox.showinfo("Success", f"Character HP address found: {hex(dialog.result)}")
    
    def find_character_tp(self):
        """Find character TP/MP address"""
        if not self.scanner.process:
            messagebox.showerror("Error", "Please attach to process first")
            return
        
        dialog = TPFinderDialog(self.root, self.scanner)
        self.root.wait_window(dialog.dialog)
        
        if dialog.result:
            self.addresses["character_tp"] = dialog.result
            self.update_addresses_display()
            messagebox.showinfo("Success", f"Character TP address found: {hex(dialog.result)}")
    
    def find_character_name(self):
        """Find character name address"""
        if not self.scanner.process:
            messagebox.showerror("Error", "Please attach to process first")
            return
        
        char_name = tk.simpledialog.askstring("Character Name", "Enter your character name:")
        if char_name:
            addresses = self.scanner.scan_for_string(char_name)
            if addresses:
                self.addresses["character_name"] = addresses[0]
                self.update_addresses_display()
                messagebox.showinfo("Success", f"Character name address found: {hex(addresses[0])}")
            else:
                messagebox.showerror("Error", f"Could not find '{char_name}' in memory")
    
    def find_target_name(self):
        """Find target name address"""
        if not self.scanner.process:
            messagebox.showerror("Error", "Please attach to process first")
            return
        
        messagebox.showinfo("Instructions", "Target a monster in game, then click OK")
        target_name = tk.simpledialog.askstring("Target Name", "Enter the target monster's name:")
        
        if target_name:
            addresses = self.scanner.scan_for_string(target_name)
            if addresses:
                self.addresses["target_name"] = addresses[0]
                self.update_addresses_display()
                messagebox.showinfo("Success", f"Target name address found: {hex(addresses[0])}")
            else:
                messagebox.showerror("Error", f"Could not find '{target_name}' in memory")
    
    def add_manual_address(self):
        """Add address manually"""
        name = self.manual_name_entry.get().strip()
        addr_str = self.manual_addr_entry.get().strip()
        
        if not name or not addr_str:
            messagebox.showerror("Error", "Please enter both name and address")
            return
        
        try:
            address = int(addr_str, 16)  # Parse as hex
            self.addresses[name] = address
            self.update_addresses_display()
            
            # Clear entries
            self.manual_name_entry.delete(0, tk.END)
            self.manual_addr_entry.delete(0, tk.END)
            
        except ValueError:
            messagebox.showerror("Error", "Invalid hex address format")
    
    def update_addresses_display(self):
        """Update the addresses treeview"""
        # Clear existing items
        for item in self.addresses_tree.get_children():
            self.addresses_tree.delete(item)
        
        # Add current addresses
        for name, address in self.addresses.items():
            current_value = "N/A"
            if self.scanner.process:
                try:
                    if "name" in name.lower():
                        current_value = self.scanner.read_value(address, "string")
                    else:
                        current_value = str(self.scanner.read_value(address, "int"))
                except:
                    current_value = "Error"
            
            self.addresses_tree.insert("", "end", values=(name, hex(address), current_value, "int"))
    
    def refresh_values(self):
        """Refresh current values in the display"""
        self.update_addresses_display()
    
    def export_addresses(self):
        """Export addresses to JSON file"""
        if not self.addresses:
            messagebox.showwarning("Warning", "No addresses to export")
            return
        
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                # Convert addresses to hex strings for JSON
                export_data = {name: hex(addr) for name, addr in self.addresses.items()}
                
                with open(filename, 'w') as f:
                    json.dump(export_data, f, indent=2)
                
                messagebox.showinfo("Success", f"Addresses exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Export failed: {e}")
    
    def generate_bot_code(self):
        """Generate Python code with found addresses"""
        if not self.addresses:
            messagebox.showwarning("Warning", "No addresses found yet")
            return
        
        code = "# Generated Memory Addresses for Tantra Bot\n"
        code += "# Copy this into your tantra_bot.py file\n\n"
        code += "class MemoryAddresses:\n"
        code += "    def __init__(self):\n"
        
        for name, address in self.addresses.items():
            var_name = name.upper().replace(" ", "_")
            code += f"        self.{var_name} = {hex(address)}\n"
        
        code += "\n# Usage example:\n"
        code += "# addresses = MemoryAddresses()\n"
        code += "# hp = memory.read_int(addresses.CHARACTER_HP)\n"
        
        # Show in new window
        code_window = tk.Toplevel(self.root)
        code_window.title("Generated Bot Code")
        code_window.geometry("600x400")
        
        code_text = scrolledtext.ScrolledText(code_window)
        code_text.pack(fill='both', expand=True, padx=10, pady=10)
        code_text.insert(1.0, code)
        
        # Copy button
        def copy_code():
            code_window.clipboard_clear()
            code_window.clipboard_append(code)
            messagebox.showinfo("Copied", "Code copied to clipboard!")
        
        ttk.Button(code_window, text="Copy to Clipboard", command=copy_code).pack(pady=5)
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

# Helper dialog classes
class HPFinderDialog:
    def __init__(self, parent, scanner):
        self.scanner = scanner
        self.result = None
        self.addresses = []
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Find Character HP Address")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
    
    def create_widgets(self):
        # Instructions
        instructions = """
1. Note your current HP in game
2. Enter it below and click 'First Scan'
3. Take damage or heal in game
4. Enter new HP and click 'Filter'
5. Repeat until only 1-2 addresses remain
        """
        
        ttk.Label(self.dialog, text=instructions, justify='left').pack(padx=10, pady=10)
        
        # HP entry
        entry_frame = ttk.Frame(self.dialog)
        entry_frame.pack(padx=10, pady=5)
        
        ttk.Label(entry_frame, text="Current HP:").pack(side='left')
        self.hp_entry = ttk.Entry(entry_frame)
        self.hp_entry.pack(side='left', padx=5)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(padx=10, pady=10)
        
        self.first_scan_btn = ttk.Button(button_frame, text="First Scan", command=self.first_scan)
        self.first_scan_btn.pack(side='left', padx=5)
        
        self.filter_btn = ttk.Button(button_frame, text="Filter", command=self.filter_scan, state='disabled')
        self.filter_btn.pack(side='left', padx=5)
        
        self.done_btn = ttk.Button(button_frame, text="Use Selected", command=self.finish, state='disabled')
        self.done_btn.pack(side='left', padx=5)
        
        # Results
        self.results_text = scrolledtext.ScrolledText(self.dialog, height=8)
        self.results_text.pack(fill='both', expand=True, padx=10, pady=5)
    
    def first_scan(self):
        try:
            hp_value = int(self.hp_entry.get())
            self.addresses = self.scanner.scan_for_value(hp_value, "int")
            
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"Found {len(self.addresses)} addresses with HP {hp_value}\n")
            
            if len(self.addresses) < 100:
                for i, addr in enumerate(self.addresses):
                    self.results_text.insert(tk.END, f"{i+1}. {hex(addr)}\n")
            
            self.filter_btn.config(state='normal')
            
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid HP number")
    
    def filter_scan(self):
        try:
            new_hp = int(self.hp_entry.get())
            filtered = []
            
            for addr in self.addresses:
                current_val = self.scanner.read_value(addr, "int")
                if current_val == new_hp:
                    filtered.append(addr)
            
            self.addresses = filtered
            
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, f"Filtered to {len(self.addresses)} addresses with HP {new_hp}\n\n")
            
            for i, addr in enumerate(self.addresses):
                self.results_text.insert(tk.END, f"{i+1}. {hex(addr)} = {self.scanner.read_value(addr, 'int')}\n")
            
            if len(self.addresses) <= 3:
                self.done_btn.config(state='normal')
                
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid HP number")
    
    def finish(self):
        if self.addresses:
            self.result = self.addresses[0]  # Use first address
        self.dialog.destroy()

class TPFinderDialog(HPFinderDialog):
    def __init__(self, parent, scanner):
        super().__init__(parent, scanner)
        self.dialog.title("Find Character TP/MP Address")

if __name__ == "__main__":
    import tkinter.simpledialog
    
    # Create and run the memory scanner
    scanner_gui = MemoryScannerGUI()
    scanner_gui.run()
