@echo off
title Tantra Bot Launcher
color 0A

echo.
echo  ========================================
echo   🎮 TANTRA BOT LAUNCHER 🎮
echo  ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo 📥 Please install Python from: https://python.org
    echo ⚠️  Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python is installed
python --version

echo.
echo Choose an option:
echo.
echo 1. 🔧 Setup Bot (First time setup)
echo 2. 🔍 Memory Scanner (Find addresses)  
echo 3. 🎮 Launch Bot (Start automation)
echo 4. ❓ Help
echo 5. 🚪 Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🔧 Starting Bot Setup...
    python setup_bot.py
) else if "%choice%"=="2" (
    echo.
    echo 🔍 Starting Memory Scanner...
    python memory_scanner.py
) else if "%choice%"=="3" (
    echo.
    echo 🎮 Starting Tantra Bot...
    python tantra_bot.py
) else if "%choice%"=="4" (
    echo.
    echo ❓ Opening Quick Start Guide...
    start QUICK_START.md
) else if "%choice%"=="5" (
    echo.
    echo 👋 Goodbye!
    exit /b 0
) else (
    echo.
    echo ❌ Invalid choice. Please try again.
    echo.
    pause
    goto :eof
)

echo.
echo 🎉 Done! Press any key to return to menu...
pause >nul
cls
goto :eof
