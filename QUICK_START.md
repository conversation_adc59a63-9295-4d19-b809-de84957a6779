# 🚀 Tantra <PERSON>t - Quick Start Guide

## 📥 **Step 1: Download & Setup (5 minutes)**

### Download Python
1. Go to [python.org](https://python.org/downloads/)
2. Download Python 3.8 or newer
3. **IMPORTANT**: Check "Add Python to PATH" during installation

### Get the Bot Files
1. Download all bot files to a folder (e.g., `C:\TantraBot\`)
2. You should have these files:
   - `setup_bot.py` - Setup script
   - `tantra_bot.py` - Main bot
   - `memory_scanner.py` - Address finder
   - `requirements.txt` - Dependencies list

## 🔧 **Step 2: Install Dependencies (5 minutes)**

### Easy Way (Recommended):
1. **Right-click** on your bot folder → "Open in Terminal" or "Open PowerShell here"
2. Run: `python setup_bot.py`
3. Click "🚀 Install Dependencies" in the GUI
4. Wait for installation to complete

### Manual Way:
```bash
pip install -r requirements.txt
```

## 🎯 **Step 3: Find Memory Addresses (10 minutes)**

**This is the most important step!** Your old AutoIt addresses won't work.

### Launch Memory Scanner:
1. **Start Tantra game** and log in to your character
2. Run: `python memory_scanner.py` (or use setup GUI)
3. Click "Attach to Tantra"

### Find Character HP:
1. Go to "Address Finder" tab
2. Click "Find Character HP"
3. Note your current HP in game (e.g., 1500)
4. Enter it and click "First Scan"
5. **Take some damage** in game (e.g., HP becomes 1200)
6. Enter new HP and click "Filter"
7. **Repeat** until you have 1-3 addresses left
8. Click "Use Selected"

### Find Other Addresses:
- **Character TP**: Same process but with TP/MP values
- **Character Name**: Enter your exact character name
- **Target Name**: Target a monster, enter its name

### Export Results:
1. Go to "Found Addresses" tab
2. Click "Export to JSON" → Save as `addresses.json`
3. Click "Generate Bot Code" → Copy the code

## 🎮 **Step 4: Configure Bot (5 minutes)**

### Launch Main Bot:
1. Run: `python tantra_bot.py`
2. The bot will automatically load `addresses.json` if it exists

### Basic Configuration:
1. **Main Controls Tab**:
   - ✅ Check "Auto Loot", "Auto Skills", "Auto Buffs"
   - Add monster names to target (one per line):
     ```
     Orc Warrior
     Goblin Shaman
     Skeleton
     ```

2. **Skills & Buffs Tab**:
   - Set skill delays (start with 2000ms each)
   - Set buff delays (30000ms = 30 seconds)

3. **Settings Tab**:
   - Set correct window title (usually "Tantra")
   - Save your configuration

## ▶️ **Step 5: Start Botting!**

1. **Position your character** in a good farming spot
2. **Make sure you have potions** and equipment ready
3. Click **"Start Bot"** in the main window
4. **Monitor the bot** for the first few minutes
5. Use **F10** for emergency stop anytime

## 🛡️ **Safety Tips**

- ⚠️ **Test on a secondary account first**
- 🕐 **Start with conservative delays** (2-3 seconds between skills)
- 👀 **Monitor the bot** - don't leave it completely unattended
- 💾 **Save working configurations** before making changes
- 🔄 **Update addresses** after game patches

## 🔧 **Troubleshooting**

### "Could not attach to process"
- ✅ Make sure Tantra is running
- ✅ Run Python as Administrator
- ✅ Check exact process name in Task Manager
- ✅ Try different process names in scanner

### "No addresses found"
- ✅ Make sure you're logged into a character
- ✅ Try more specific values (exact HP numbers)
- ✅ Take bigger damage/healing for clearer changes
- ✅ Use different value types (int, short, byte)

### "Bot not working properly"
- ✅ Verify memory addresses are correct
- ✅ Check window title matches exactly
- ✅ Increase skill delays if too fast
- ✅ Make sure character has enough TP/MP

### "Skills not casting"
- ✅ Check game key bindings (1,2,3,4 for skills)
- ✅ Verify game window has focus
- ✅ Increase delays between skills
- ✅ Check TP requirements for skills

## 📊 **Performance Tips**

### For Better Speed:
- Use **C# version** for maximum performance
- **Lower skill delays** (but test carefully)
- **Disable unnecessary features** you don't need
- **Close other programs** to free up resources

### For Better Stealth:
- **Randomize delays**: Add ±500ms variation
- **Human-like patterns**: Don't use perfect timing
- **Take breaks**: Stop bot periodically
- **Monitor behavior**: Make sure it looks natural

## 🎯 **Advanced Features**

### Custom Monster Lists:
```
# Target specific monsters only
Elite Orc
Boss Goblin
Rare Skeleton

# Or use partial names
Orc
Goblin
Skeleton
```

### Skill Rotation:
- **Skill1**: Main attack (2000ms delay)
- **Skill2**: Secondary attack (3000ms delay)  
- **Skill3**: Special ability (4000ms delay)
- **Skill4**: Ultimate skill (5000ms delay)

### Buff Management:
- **Buff1**: Short buff (30 seconds)
- **Buff2**: Medium buff (45 seconds)
- **Buff3**: Long buff (60 seconds)
- **Buff4**: Very long buff (2 minutes)

## 🆘 **Emergency Procedures**

### If Bot Goes Crazy:
1. **Press F10** (emergency stop)
2. **Alt+Tab** to bot window
3. **Click "Stop Bot"**
4. **Check what went wrong**

### If Character Dies:
1. **Stop the bot immediately**
2. **Revive your character**
3. **Check HP/TP addresses** (might have changed)
4. **Restart bot carefully**

### If Game Updates:
1. **Stop all bots**
2. **Run memory scanner again**
3. **Find new addresses**
4. **Update bot configuration**
5. **Test thoroughly before extended use**

## 🎉 **You're Ready!**

Your bot should now be working! Remember:

- **Start slow** and increase speed gradually
- **Monitor performance** and adjust as needed
- **Keep backups** of working configurations
- **Stay updated** with game changes

**Happy Botting! 🎮✨**

---

💡 **Need more help?** Check the full README.md or use the help button in setup_bot.py
