#!/usr/bin/env python3
"""
Tantra Bot Setup Script
Automatically installs dependencies and sets up the bot
"""

import subprocess
import sys
import os
import platform
import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser

class BotSetup:
    def __init__(self):
        self.setup_gui()
        
    def setup_gui(self):
        """Create setup GUI"""
        self.root = tk.Tk()
        self.root.title("Kathana Bot Setup")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # Header
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="🎮 Kathana Bot Setup",
                              font=('Arial', 20, 'bold'),
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # Main content
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # System info
        info_frame = ttk.LabelFrame(main_frame, text="System Information")
        info_frame.pack(fill='x', pady=(0, 10))
        
        system_info = f"""
Python Version: {sys.version.split()[0]}
Platform: {platform.system()} {platform.release()}
Architecture: {platform.machine()}
        """.strip()
        
        ttk.Label(info_frame, text=system_info, font=('Courier', 9)).pack(padx=10, pady=10)
        
        # Setup steps
        steps_frame = ttk.LabelFrame(main_frame, text="Setup Steps")
        steps_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        self.progress = ttk.Progressbar(steps_frame, mode='determinate')
        self.progress.pack(fill='x', padx=10, pady=5)
        
        self.status_text = tk.Text(steps_frame, height=15, font=('Courier', 9))
        scrollbar = ttk.Scrollbar(steps_frame, orient='vertical', command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=5)
        scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=5)
        
        # Buttons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x')
        
        self.install_btn = ttk.Button(button_frame, text="🚀 Install Dependencies", 
                                     command=self.install_dependencies)
        self.install_btn.pack(side='left', padx=(0, 10))
        
        self.scanner_btn = ttk.Button(button_frame, text="🔍 Open Memory Scanner", 
                                     command=self.open_scanner, state='disabled')
        self.scanner_btn.pack(side='left', padx=(0, 10))
        
        self.bot_btn = ttk.Button(button_frame, text="🎮 Launch Bot", 
                                 command=self.launch_bot, state='disabled')
        self.bot_btn.pack(side='left', padx=(0, 10))
        
        self.help_btn = ttk.Button(button_frame, text="❓ Help", 
                                  command=self.show_help)
        self.help_btn.pack(side='right')
        
        # Initial status
        self.log("Welcome to Kathana Bot Setup!")
        self.log("Click 'Install Dependencies' to begin setup.")
        self.log("")
        self.check_python_version()
        
    def log(self, message):
        """Add message to status log"""
        self.status_text.insert(tk.END, message + "\n")
        self.status_text.see(tk.END)
        self.root.update()
        
    def check_python_version(self):
        """Check if Python version is compatible"""
        version = sys.version_info
        if version.major == 3 and version.minor >= 8:
            self.log(f"✅ Python {version.major}.{version.minor} is compatible")
        elif version.major == 3 and version.minor >= 6:
            self.log(f"⚠️ Python {version.major}.{version.minor} might work but 3.8+ recommended")
        else:
            self.log(f"❌ Python {version.major}.{version.minor} is too old!")
            self.log("Please install Python 3.8 or newer from python.org")
            
    def install_dependencies(self):
        """Install required Python packages"""
        self.install_btn.config(state='disabled')
        self.progress['value'] = 0
        
        packages = [
            'pymem>=1.10.0',
            'pywin32>=306', 
            'pyautogui>=0.9.54',
            'keyboard>=0.13.5',
            'opencv-python>=4.8.0',
            'Pillow>=10.0.0',
            'numpy>=1.24.0',
            'psutil>=5.9.0'
        ]
        
        self.log("🔄 Installing Python packages...")
        self.log("This may take a few minutes...")
        self.log("")
        
        total_packages = len(packages)
        
        for i, package in enumerate(packages):
            self.log(f"Installing {package}...")
            
            try:
                # Run pip install
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    self.log(f"✅ {package} installed successfully")
                else:
                    self.log(f"❌ Failed to install {package}")
                    self.log(f"Error: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                self.log(f"⏰ Timeout installing {package}")
            except Exception as e:
                self.log(f"❌ Error installing {package}: {e}")
            
            # Update progress
            self.progress['value'] = ((i + 1) / total_packages) * 100
            self.root.update()
        
        self.log("")
        self.log("🎉 Installation complete!")
        self.log("You can now use the Memory Scanner and Bot.")
        
        # Enable other buttons
        self.scanner_btn.config(state='normal')
        self.bot_btn.config(state='normal')
        self.install_btn.config(state='normal', text='🔄 Reinstall Dependencies')
        
    def open_scanner(self):
        """Open the memory scanner"""
        try:
            if os.path.exists('memory_scanner.py'):
                subprocess.Popen([sys.executable, 'memory_scanner.py'])
                self.log("🔍 Memory Scanner opened in new window")
            else:
                messagebox.showerror("Error", "memory_scanner.py not found!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open scanner: {e}")
            
    def launch_bot(self):
        """Launch the main bot"""
        try:
            if os.path.exists('tantra_bot.py'):
                subprocess.Popen([sys.executable, 'tantra_bot.py'])
                self.log("🎮 Tantra Bot launched in new window")
            else:
                messagebox.showerror("Error", "tantra_bot.py not found!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch bot: {e}")
            
    def show_help(self):
        """Show help information"""
        help_window = tk.Toplevel(self.root)
        help_window.title("Tantra Bot Help")
        help_window.geometry("700x600")
        
        help_text = """
🎮 TANTRA BOT SETUP HELP

📋 SETUP PROCESS:
1. Click "Install Dependencies" to install required Python packages
2. Use "Memory Scanner" to find correct memory addresses for your game
3. Launch the bot and configure your settings
4. Start automation!

🔍 MEMORY SCANNER USAGE:
1. Start Tantra game and log in to a character
2. Open Memory Scanner
3. Click "Attach to Tantra" 
4. Use "Address Finder" tab to find:
   - Character HP (note HP, take damage, scan again)
   - Character TP/MP (use skills, scan for new value)
   - Character Name (enter your character name)
   - Target Name (target a monster, enter its name)
5. Export addresses and copy code to bot

🎮 BOT CONFIGURATION:
1. Set correct window title in Settings tab
2. Add target monster names (one per line)
3. Adjust skill delays for your character
4. Configure auto features (loot, skills, buffs)
5. Save configuration for future use

⚠️ TROUBLESHOOTING:
- Run as Administrator if memory access fails
- Check that Tantra process name is correct
- Update memory addresses after game updates
- Disable antivirus if it blocks the bot

🔗 USEFUL LINKS:
- Python Download: https://python.org
- Cheat Engine (for advanced users): https://cheatengine.org
- AutoIt Documentation: https://autoitscript.com

💡 TIPS:
- Always test on a secondary account first
- Use conservative delays to avoid detection
- Monitor bot behavior and adjust as needed
- Keep backups of working configurations

❓ NEED MORE HELP?
Check the README.md file for detailed instructions.
        """
        
        text_widget = tk.Text(help_window, wrap='word', padx=10, pady=10, font=('Arial', 10))
        scrollbar_help = ttk.Scrollbar(help_window, orient='vertical', command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar_help.set)
        
        text_widget.pack(side='left', fill='both', expand=True)
        scrollbar_help.pack(side='right', fill='y')
        
        text_widget.insert('1.0', help_text)
        text_widget.config(state='disabled')
        
    def run(self):
        """Run the setup GUI"""
        self.root.mainloop()

def check_admin():
    """Check if running as administrator on Windows"""
    if platform.system() == 'Windows':
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    return True

def main():
    """Main setup function"""
    print("🎮 Tantra Bot Setup Starting...")
    
    # Check if running as admin (recommended for memory access)
    if not check_admin():
        print("⚠️ Not running as Administrator")
        print("💡 For best results, run as Administrator for memory access")
    
    # Create and run setup GUI
    setup = BotSetup()
    setup.run()

if __name__ == "__main__":
    main()
