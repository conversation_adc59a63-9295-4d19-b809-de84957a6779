#!/usr/bin/env python3
"""
Tantra Bot System - Python Implementation
Modern rewrite of the AutoIt bot with enhanced features
"""

import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional
import pymem
import pymem.process
import win32gui
import win32con
import win32api
import win32process
import keyboard
import pyautogui
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class BotConfig:
    """Bot configuration settings"""
    window_title: str = "Tantra"
    auto_loot: bool = True
    loot_duration: int = 1000
    auto_repair: bool = True
    auto_skills: bool = True
    auto_buffs: bool = True
    monster_list: List[str] = None
    skill_delays: Dict[str, int] = None
    buff_delays: Dict[str, int] = None
    auto_reply_pm: bool = False
    reply_message: str = "AFK - Auto Reply"
    anti_stuck: bool = True
    
    def __post_init__(self):
        if self.monster_list is None:
            self.monster_list = []
        if self.skill_delays is None:
            self.skill_delays = {
                'skill1': 2000, 'skill2': 3000, 
                'skill3': 4000, 'skill4': 5000
            }
        if self.buff_delays is None:
            self.buff_delays = {
                'buff1': 30000, 'buff2': 45000,
                'buff3': 60000, 'buff4': 120000
            }

class MemoryAddresses:
    """Memory addresses for Tantra game - Update these with memory scanner"""

    def __init__(self):
        # Default placeholder addresses - MUST be updated with memory_scanner.py
        self.CHARACTER_HP = 0x12345678      # Character current HP
        self.CHARACTER_MAX_HP = 0x1234567C  # Character maximum HP
        self.CHARACTER_TP = 0x12345680      # Character current TP/MP
        self.CHARACTER_MAX_TP = 0x12345684  # Character maximum TP/MP
        self.CHARACTER_NAME = 0x12345688    # Character name string
        self.TARGET_NAME = 0x12345690       # Current target name
        self.TARGET_HP = 0x12345698         # Target current HP
        self.CHARACTER_LEVEL = 0x123456A0   # Character level
        self.CHARACTER_EXP = 0x123456A4     # Character experience

        # Load addresses from file if exists
        self.load_from_file()

    def load_from_file(self, filename: str = "addresses.json"):
        """Load addresses from JSON file"""
        try:
            import os
            if os.path.exists(filename):
                with open(filename, 'r') as f:
                    data = json.load(f)

                for key, value in data.items():
                    if hasattr(self, key.upper()):
                        # Convert hex string back to int
                        if isinstance(value, str) and value.startswith('0x'):
                            setattr(self, key.upper(), int(value, 16))
                        else:
                            setattr(self, key.upper(), value)

                logger.info(f"Loaded addresses from {filename}")
        except Exception as e:
            logger.warning(f"Could not load addresses from file: {e}")

    def save_to_file(self, filename: str = "addresses.json"):
        """Save addresses to JSON file"""
        try:
            data = {}
            for attr_name in dir(self):
                if not attr_name.startswith('_') and attr_name.isupper():
                    value = getattr(self, attr_name)
                    if isinstance(value, int):
                        data[attr_name.lower()] = hex(value)

            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Saved addresses to {filename}")
        except Exception as e:
            logger.error(f"Could not save addresses: {e}")

class MemoryManager:
    """Enhanced memory reading/writing operations"""

    def __init__(self, addresses: MemoryAddresses):
        self.addresses = addresses
        self.process = None
        self.base_address = None
        self.process_name = None

    def attach_to_process(self, process_name: str = None) -> bool:
        """Attach to the game process - tries multiple common names"""
        possible_names = [
            process_name or "Tantra",
            "Tantra.exe",
            "tantra",
            "client.exe",
            "game.exe"
        ]

        for name in possible_names:
            if name is None:
                continue

            try:
                self.process = pymem.Pymem(name)
                self.base_address = self.process.base_address
                self.process_name = name
                logger.info(f"✅ Attached to {name} (PID: {self.process.process_id})")
                logger.info(f"📍 Base Address: {hex(self.base_address)}")
                return True
            except Exception:
                continue

        logger.error("❌ Failed to attach to any Tantra process")
        logger.info("💡 Make sure Tantra is running and try these steps:")
        logger.info("   1. Run this program as Administrator")
        logger.info("   2. Check the exact process name in Task Manager")
        logger.info("   3. Use the Memory Scanner to find the correct process")
        return False

    def is_process_alive(self) -> bool:
        """Check if the attached process is still running"""
        try:
            if self.process is None:
                return False
            # Try to read a small amount of memory to test connection
            self.process.read_bytes(self.base_address, 4)
            return True
        except:
            return False

    def read_int(self, address: int) -> int:
        """Read 32-bit integer from memory"""
        try:
            if not self.process:
                return 0
            return self.process.read_int(address)
        except Exception as e:
            logger.debug(f"Failed to read int at {hex(address)}: {e}")
            return 0

    def read_short(self, address: int) -> int:
        """Read 16-bit integer from memory"""
        try:
            if not self.process:
                return 0
            return self.process.read_short(address)
        except Exception as e:
            logger.debug(f"Failed to read short at {hex(address)}: {e}")
            return 0

    def read_byte(self, address: int) -> int:
        """Read single byte from memory"""
        try:
            if not self.process:
                return 0
            return self.process.read_uchar(address)
        except Exception as e:
            logger.debug(f"Failed to read byte at {hex(address)}: {e}")
            return 0

    def read_float(self, address: int) -> float:
        """Read float from memory"""
        try:
            if not self.process:
                return 0.0
            return self.process.read_float(address)
        except Exception as e:
            logger.debug(f"Failed to read float at {hex(address)}: {e}")
            return 0.0

    def read_string(self, address: int, length: int = 50) -> str:
        """Read string from memory"""
        try:
            if not self.process:
                return ""
            result = self.process.read_string(address, length)
            return result.strip('\x00') if result else ""
        except Exception as e:
            logger.debug(f"Failed to read string at {hex(address)}: {e}")
            return ""

    def write_int(self, address: int, value: int) -> bool:
        """Write 32-bit integer to memory"""
        try:
            if not self.process:
                return False
            self.process.write_int(address, value)
            return True
        except Exception as e:
            logger.debug(f"Failed to write int to {hex(address)}: {e}")
            return False

    def write_bytes(self, address: int, data: bytes) -> bool:
        """Write bytes to memory"""
        try:
            if not self.process:
                return False
            self.process.write_bytes(address, data, len(data))
            return True
        except Exception as e:
            logger.debug(f"Failed to write bytes to {hex(address)}: {e}")
            return False

class GameController:
    """Enhanced game window interaction and automation"""

    def __init__(self, config: BotConfig):
        self.config = config
        self.addresses = MemoryAddresses()
        self.memory = MemoryManager(self.addresses)
        self.window_handle = None
        self.is_running = False
        self.last_skill_times = {}
        self.last_buff_times = {}
        self.character_stats = {}
        self.last_position_check = 0
        self.stuck_counter = 0
        
    def find_game_window(self) -> bool:
        """Find and attach to game window"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if self.config.window_title.lower() in window_text.lower():
                    windows.append((hwnd, window_text))
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            self.window_handle = windows[0][0]
            logger.info(f"Found game window: {windows[0][1]}")
            return True
        
        logger.error("Game window not found")
        return False
    
    def send_key(self, key: str, hold_time: float = 0.1):
        """Send key to game window"""
        if not self.window_handle:
            return
        
        try:
            # Bring window to foreground
            win32gui.SetForegroundWindow(self.window_handle)
            time.sleep(0.05)
            
            # Send key
            if hold_time > 0:
                pyautogui.keyDown(key)
                time.sleep(hold_time)
                pyautogui.keyUp(key)
            else:
                pyautogui.press(key)
                
        except Exception as e:
            logger.error(f"Failed to send key '{key}': {e}")
    
    def get_character_hp(self) -> int:
        """Get character's current HP from memory"""
        return self.memory.read_int(self.addresses.CHARACTER_HP)

    def get_character_max_hp(self) -> int:
        """Get character's maximum HP from memory"""
        return self.memory.read_int(self.addresses.CHARACTER_MAX_HP)

    def get_character_tp(self) -> int:
        """Get character's current TP from memory"""
        return self.memory.read_int(self.addresses.CHARACTER_TP)

    def get_character_max_tp(self) -> int:
        """Get character's maximum TP from memory"""
        return self.memory.read_int(self.addresses.CHARACTER_MAX_TP)

    def get_character_name(self) -> str:
        """Get character's name from memory"""
        return self.memory.read_string(self.addresses.CHARACTER_NAME)

    def get_target_name(self) -> str:
        """Get current target's name"""
        return self.memory.read_string(self.addresses.TARGET_NAME)

    def get_target_hp(self) -> int:
        """Get target's current HP"""
        return self.memory.read_int(self.addresses.TARGET_HP)

    def get_character_level(self) -> int:
        """Get character's level"""
        return self.memory.read_int(self.addresses.CHARACTER_LEVEL)

    def update_character_stats(self):
        """Update character statistics"""
        self.character_stats = {
            'hp': self.get_character_hp(),
            'max_hp': self.get_character_max_hp(),
            'tp': self.get_character_tp(),
            'max_tp': self.get_character_max_tp(),
            'name': self.get_character_name(),
            'level': self.get_character_level(),
            'target_name': self.get_target_name(),
            'target_hp': self.get_target_hp()
        }
    
    def find_target(self):
        """Find and target appropriate monster"""
        if not self.config.monster_list:
            self.send_key('t')  # Target nearest enemy
            return
        
        # Send target enemy command
        self.send_key('t')
        time.sleep(0.5)
        
        # Check if target is in our monster list
        target_name = self.get_target_name()
        if target_name and any(monster.lower() in target_name.lower() 
                              for monster in self.config.monster_list):
            logger.info(f"Targeting: {target_name}")
            return True
        
        # If not valid target, try to find another
        self.send_key('a', 0.2)  # Turn to find monsters
        time.sleep(0.3)
        return False
    
    def use_skill(self, skill_key: str, skill_name: str):
        """Use a skill with cooldown management"""
        current_time = time.time() * 1000
        last_use = self.last_skill_times.get(skill_name, 0)
        cooldown = self.config.skill_delays.get(skill_name, 2000)
        
        if current_time - last_use >= cooldown:
            self.send_key(skill_key)
            self.last_skill_times[skill_name] = current_time
            logger.debug(f"Used skill: {skill_name}")
    
    def use_buff(self, buff_key: str, buff_name: str):
        """Use a buff with cooldown management"""
        current_time = time.time() * 1000
        last_use = self.last_buff_times.get(buff_name, 0)
        cooldown = self.config.buff_delays.get(buff_name, 30000)
        
        if current_time - last_use >= cooldown:
            self.send_key(buff_key)
            self.last_buff_times[buff_name] = current_time
            logger.debug(f"Used buff: {buff_name}")
    
    def auto_loot(self):
        """Automatically loot nearby items"""
        if self.config.auto_loot:
            self.send_key('f', self.config.loot_duration / 1000.0)
    
    def check_health(self):
        """Check and manage character health"""
        hp = self.get_character_hp()
        # Add healing logic here
        if hp < 30:  # Low health threshold
            self.send_key('1')  # Use healing potion (example)
    
    def anti_stuck_movement(self):
        """Perform anti-stuck movement"""
        if self.config.anti_stuck:
            self.send_key('a', 0.3)  # Turn
            time.sleep(0.1)
            self.send_key('w', 0.5)  # Move forward
    
    def main_bot_loop(self):
        """Main automation loop"""
        logger.info("Starting bot main loop")
        
        while self.is_running:
            try:
                # Check if game window still exists
                if not win32gui.IsWindow(self.window_handle):
                    logger.warning("Game window lost, searching again...")
                    if not self.find_game_window():
                        time.sleep(5)
                        continue
                
                # Core bot sequence
                self.check_health()
                
                # Use buffs
                if self.config.auto_buffs:
                    self.use_buff('f2', 'buff1')
                    self.use_buff('f3', 'buff2')
                
                # Find and attack target
                if self.find_target():
                    if self.config.auto_skills:
                        self.use_skill('1', 'skill1')
                        time.sleep(0.5)
                        self.use_skill('2', 'skill2')
                        time.sleep(0.5)
                        self.use_skill('3', 'skill3')
                        time.sleep(0.5)
                        self.use_skill('4', 'skill4')
                
                # Loot items
                self.auto_loot()
                
                # Anti-stuck movement occasionally
                if time.time() % 30 < 1:  # Every 30 seconds
                    self.anti_stuck_movement()
                
                time.sleep(0.1)  # Small delay to prevent excessive CPU usage
                
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                time.sleep(1)
    
    def start_bot(self):
        """Start the bot"""
        if not self.memory.attach_to_process():
            messagebox.showerror("Error", "Could not attach to game process")
            return False
        
        if not self.find_game_window():
            messagebox.showerror("Error", "Could not find game window")
            return False
        
        self.is_running = True
        self.bot_thread = threading.Thread(target=self.main_bot_loop, daemon=True)
        self.bot_thread.start()
        return True
    
    def stop_bot(self):
        """Stop the bot"""
        self.is_running = False
        logger.info("Bot stopped")

class BotGUI:
    """Main GUI for the bot"""
    
    def __init__(self):
        self.config = BotConfig()
        self.controller = GameController(self.config)
        self.setup_gui()
        self.setup_hotkeys()
    
    def setup_gui(self):
        """Create the main GUI"""
        self.root = tk.Tk()
        self.root.title("Tantra Bot System - Python Edition v2.0")
        self.root.geometry("600x800")
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Main tab
        main_frame = ttk.Frame(notebook)
        notebook.add(main_frame, text="Main Controls")
        self.create_main_tab(main_frame)
        
        # Skills tab
        skills_frame = ttk.Frame(notebook)
        notebook.add(skills_frame, text="Skills & Buffs")
        self.create_skills_tab(skills_frame)
        
        # Settings tab
        settings_frame = ttk.Frame(notebook)
        notebook.add(settings_frame, text="Settings")
        self.create_settings_tab(settings_frame)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief='sunken')
        status_bar.pack(side='bottom', fill='x')
    
    def create_main_tab(self, parent):
        """Create main control tab"""
        # Start/Stop buttons
        button_frame = ttk.Frame(parent)
        button_frame.pack(pady=10)
        
        self.start_button = ttk.Button(button_frame, text="Start Bot", 
                                      command=self.start_bot, style='Accent.TButton')
        self.start_button.pack(side='left', padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="Stop Bot", 
                                     command=self.stop_bot, state='disabled')
        self.stop_button.pack(side='left', padx=5)
        
        # Auto features
        features_frame = ttk.LabelFrame(parent, text="Auto Features")
        features_frame.pack(fill='x', padx=10, pady=5)
        
        self.auto_loot_var = tk.BooleanVar(value=self.config.auto_loot)
        ttk.Checkbutton(features_frame, text="Auto Loot", 
                       variable=self.auto_loot_var).pack(anchor='w')
        
        self.auto_skills_var = tk.BooleanVar(value=self.config.auto_skills)
        ttk.Checkbutton(features_frame, text="Auto Skills", 
                       variable=self.auto_skills_var).pack(anchor='w')
        
        self.auto_buffs_var = tk.BooleanVar(value=self.config.auto_buffs)
        ttk.Checkbutton(features_frame, text="Auto Buffs", 
                       variable=self.auto_buffs_var).pack(anchor='w')
        
        # Monster list
        monster_frame = ttk.LabelFrame(parent, text="Target Monsters")
        monster_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.monster_text = tk.Text(monster_frame, height=10)
        self.monster_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Load default monsters
        default_monsters = ["Orc", "Goblin", "Skeleton", "Wolf"]
        self.monster_text.insert('1.0', '\n'.join(default_monsters))
    
    def create_skills_tab(self, parent):
        """Create skills configuration tab"""
        # Skill delays
        skills_frame = ttk.LabelFrame(parent, text="Skill Delays (ms)")
        skills_frame.pack(fill='x', padx=10, pady=5)
        
        self.skill_vars = {}
        for i, (skill, delay) in enumerate(self.config.skill_delays.items()):
            row_frame = ttk.Frame(skills_frame)
            row_frame.pack(fill='x', padx=5, pady=2)
            
            ttk.Label(row_frame, text=f"{skill.title()}:").pack(side='left')
            var = tk.IntVar(value=delay)
            self.skill_vars[skill] = var
            ttk.Entry(row_frame, textvariable=var, width=10).pack(side='right')
        
        # Buff delays
        buffs_frame = ttk.LabelFrame(parent, text="Buff Delays (ms)")
        buffs_frame.pack(fill='x', padx=10, pady=5)
        
        self.buff_vars = {}
        for buff, delay in self.config.buff_delays.items():
            row_frame = ttk.Frame(buffs_frame)
            row_frame.pack(fill='x', padx=5, pady=2)
            
            ttk.Label(row_frame, text=f"{buff.title()}:").pack(side='left')
            var = tk.IntVar(value=delay)
            self.buff_vars[buff] = var
            ttk.Entry(row_frame, textvariable=var, width=10).pack(side='right')
    
    def create_settings_tab(self, parent):
        """Create settings tab"""
        # File operations
        file_frame = ttk.LabelFrame(parent, text="Configuration")
        file_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(file_frame, text="Save Config", 
                  command=self.save_config).pack(side='left', padx=5, pady=5)
        ttk.Button(file_frame, text="Load Config", 
                  command=self.load_config).pack(side='left', padx=5, pady=5)
        
        # Window title
        window_frame = ttk.LabelFrame(parent, text="Game Window")
        window_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(window_frame, text="Window Title:").pack(anchor='w')
        self.window_title_var = tk.StringVar(value=self.config.window_title)
        ttk.Entry(window_frame, textvariable=self.window_title_var).pack(fill='x', padx=5, pady=2)
    
    def setup_hotkeys(self):
        """Setup global hotkeys"""
        try:
            keyboard.add_hotkey('f10', self.emergency_stop)
            keyboard.add_hotkey('shift+alt+m', self.show_gui)
        except Exception as e:
            logger.warning(f"Could not setup hotkeys: {e}")
    
    def start_bot(self):
        """Start the bot"""
        self.update_config_from_gui()
        
        if self.controller.start_bot():
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            self.status_var.set("Bot Running...")
            logger.info("Bot started successfully")
        else:
            messagebox.showerror("Error", "Failed to start bot")
    
    def stop_bot(self):
        """Stop the bot"""
        self.controller.stop_bot()
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.status_var.set("Bot Stopped")
    
    def emergency_stop(self):
        """Emergency stop hotkey"""
        self.stop_bot()
        messagebox.showinfo("Emergency Stop", "Bot has been stopped!")
    
    def show_gui(self):
        """Show GUI hotkey"""
        self.root.deiconify()
        self.root.lift()
    
    def update_config_from_gui(self):
        """Update config from GUI values"""
        self.config.auto_loot = self.auto_loot_var.get()
        self.config.auto_skills = self.auto_skills_var.get()
        self.config.auto_buffs = self.auto_buffs_var.get()
        self.config.window_title = self.window_title_var.get()
        
        # Update monster list
        monster_text = self.monster_text.get('1.0', 'end-1c')
        self.config.monster_list = [m.strip() for m in monster_text.split('\n') if m.strip()]
        
        # Update skill delays
        for skill, var in self.skill_vars.items():
            self.config.skill_delays[skill] = var.get()
        
        # Update buff delays
        for buff, var in self.buff_vars.items():
            self.config.buff_delays[buff] = var.get()
    
    def save_config(self):
        """Save configuration to file"""
        self.update_config_from_gui()
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.config.__dict__, f, indent=2)
                messagebox.showinfo("Success", "Configuration saved!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save config: {e}")
    
    def load_config(self):
        """Load configuration from file"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)
                
                # Update config
                for key, value in data.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                
                # Update GUI
                self.auto_loot_var.set(self.config.auto_loot)
                self.auto_skills_var.set(self.config.auto_skills)
                self.auto_buffs_var.set(self.config.auto_buffs)
                self.window_title_var.set(self.config.window_title)
                
                # Update monster list
                self.monster_text.delete('1.0', 'end')
                self.monster_text.insert('1.0', '\n'.join(self.config.monster_list))
                
                messagebox.showinfo("Success", "Configuration loaded!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load config: {e}")
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    # Create and run the bot
    bot_gui = BotGUI()
    bot_gui.run()
