# Tantra Bot System - Python Requirements
# Install with: pip install -r requirements.txt

# Memory manipulation
pymem>=1.10.0

# GUI framework
tkinter  # Usually included with Python

# Windows API access
pywin32>=306

# Keyboard and mouse automation
pyautogui>=0.9.54
keyboard>=0.13.5

# Image processing (optional, for advanced features)
opencv-python>=4.8.0
Pillow>=10.0.0

# Utilities
numpy>=1.24.0
psutil>=5.9.0

# Configuration and logging
pyyaml>=6.0
colorlog>=6.7.0

# Optional: For advanced memory scanning
capstone>=5.0.0  # Disassembly engine
