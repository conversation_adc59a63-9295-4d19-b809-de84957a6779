#!/usr/bin/env python3
"""
FULLY AUTOMATED Tantra Bot Setup
This script does EVERYTHING automatically - no user input needed
"""

import subprocess
import sys
import os
import platform
import urllib.request
import zipfile
import shutil
import time
import json
from pathlib import Path

class AutoSetup:
    def __init__(self):
        self.bot_dir = Path.cwd()
        self.python_installed = False
        self.packages_installed = False
        
    def log(self, message, level="INFO"):
        """Enhanced logging with colors and timestamps"""
        timestamp = time.strftime("%H:%M:%S")
        colors = {
            "INFO": "\033[92m",    # Green
            "WARN": "\033[93m",    # Yellow  
            "ERROR": "\033[91m",   # Red
            "SUCCESS": "\033[96m", # Cyan
            "RESET": "\033[0m"     # Reset
        }
        
        color = colors.get(level, colors["INFO"])
        reset = colors["RESET"]
        
        print(f"{color}[{timestamp}] {level}: {message}{reset}")
        
    def check_admin(self):
        """Check if running as administrator"""
        try:
            if platform.system() == "Windows":
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin()
            return os.geteuid() == 0
        except:
            return False
    
    def install_python_if_needed(self):
        """Automatically install Python if not present"""
        self.log("🔍 Checking Python installation...")
        
        try:
            result = subprocess.run([sys.executable, "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                self.log(f"✅ Python found: {version}", "SUCCESS")
                self.python_installed = True
                return True
        except:
            pass
        
        # Try alternative python commands
        for cmd in ["python", "python3", "py"]:
            try:
                result = subprocess.run([cmd, "--version"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    self.log(f"✅ Python found via '{cmd}': {version}", "SUCCESS")
                    self.python_installed = True
                    return True
            except:
                continue
        
        self.log("❌ Python not found. Installing automatically...", "WARN")
        return self.auto_install_python()
    
    def auto_install_python(self):
        """Automatically download and install Python"""
        if platform.system() != "Windows":
            self.log("❌ Auto-install only supported on Windows", "ERROR")
            self.log("Please install Python manually from python.org")
            return False
        
        try:
            self.log("📥 Downloading Python installer...")
            
            # Download Python installer
            python_url = "https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe"
            installer_path = self.bot_dir / "python_installer.exe"
            
            urllib.request.urlretrieve(python_url, installer_path)
            self.log("✅ Python installer downloaded")
            
            # Run installer silently
            self.log("🔧 Installing Python (this may take a few minutes)...")
            
            install_cmd = [
                str(installer_path),
                "/quiet",           # Silent install
                "InstallAllUsers=1", # Install for all users
                "PrependPath=1",    # Add to PATH
                "Include_test=0"    # Skip tests
            ]
            
            result = subprocess.run(install_cmd, timeout=600)  # 10 minute timeout
            
            if result.returncode == 0:
                self.log("✅ Python installed successfully!", "SUCCESS")
                
                # Clean up installer
                installer_path.unlink()
                
                # Verify installation
                time.sleep(5)  # Wait for PATH update
                return self.verify_python_install()
            else:
                self.log("❌ Python installation failed", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Error installing Python: {e}", "ERROR")
            return False
    
    def verify_python_install(self):
        """Verify Python is properly installed"""
        for cmd in ["python", "py", sys.executable]:
            try:
                result = subprocess.run([cmd, "--version"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.log(f"✅ Python verification successful: {result.stdout.strip()}", "SUCCESS")
                    return True
            except:
                continue
        
        self.log("❌ Python verification failed", "ERROR")
        return False
    
    def install_packages(self):
        """Install all required Python packages"""
        self.log("📦 Installing Python packages...")
        
        packages = [
            "pymem>=1.10.0",
            "pywin32>=306",
            "pyautogui>=0.9.54", 
            "keyboard>=0.13.5",
            "opencv-python>=4.8.0",
            "Pillow>=10.0.0",
            "numpy>=1.24.0",
            "psutil>=5.9.0"
        ]
        
        failed_packages = []
        
        for package in packages:
            self.log(f"Installing {package}...")
            
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package, "--upgrade"
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    self.log(f"✅ {package} installed successfully")
                else:
                    self.log(f"❌ Failed to install {package}: {result.stderr}", "ERROR")
                    failed_packages.append(package)
                    
            except subprocess.TimeoutExpired:
                self.log(f"⏰ Timeout installing {package}", "WARN")
                failed_packages.append(package)
            except Exception as e:
                self.log(f"❌ Error installing {package}: {e}", "ERROR")
                failed_packages.append(package)
        
        if failed_packages:
            self.log(f"❌ Failed to install: {', '.join(failed_packages)}", "ERROR")
            return False
        
        self.log("✅ All packages installed successfully!", "SUCCESS")
        self.packages_installed = True
        return True
    
    def create_default_config(self):
        """Create default configuration files"""
        self.log("⚙️ Creating default configuration...")
        
        # Default addresses (placeholders)
        default_addresses = {
            "character_hp": "0x12345678",
            "character_max_hp": "0x1234567C", 
            "character_tp": "0x12345680",
            "character_max_tp": "0x12345684",
            "character_name": "0x12345688",
            "target_name": "0x12345690",
            "target_hp": "0x12345698"
        }
        
        with open("addresses.json", "w") as f:
            json.dump(default_addresses, f, indent=2)
        
        # Default bot config
        default_config = {
            "window_title": "Tantra",
            "auto_loot": True,
            "auto_skills": True,
            "auto_buffs": True,
            "monster_list": ["Orc", "Goblin", "Skeleton", "Wolf"],
            "skill_delays": {
                "skill1": 2000,
                "skill2": 3000, 
                "skill3": 4000,
                "skill4": 5000
            },
            "buff_delays": {
                "buff1": 30000,
                "buff2": 45000,
                "buff3": 60000,
                "buff4": 120000
            }
        }
        
        with open("bot_config.json", "w") as f:
            json.dump(default_config, f, indent=2)
        
        self.log("✅ Default configuration created")
    
    def create_launcher_scripts(self):
        """Create easy launcher scripts"""
        self.log("🚀 Creating launcher scripts...")
        
        # Windows batch file
        batch_content = '''@echo off
title Tantra Bot Launcher
echo.
echo ========================================
echo   🎮 TANTRA BOT LAUNCHER 🎮  
echo ========================================
echo.

echo 1. 🔍 Memory Scanner (Find addresses)
echo 2. 🎮 Launch Bot (Start automation)
echo 3. ❓ Help
echo.

set /p choice="Enter choice (1-3): "

if "%choice%"=="1" (
    echo Starting Memory Scanner...
    python memory_scanner.py
) else if "%choice%"=="2" (
    echo Starting Tantra Bot...
    python tantra_bot.py
) else if "%choice%"=="3" (
    echo Opening help...
    start QUICK_START.md
) else (
    echo Invalid choice
)

pause
'''
        
        with open("launch_bot.bat", "w") as f:
            f.write(batch_content)
        
        # Python launcher
        launcher_content = '''#!/usr/bin/env python3
import subprocess
import sys
import os

def main():
    print("🎮 Tantra Bot Launcher")
    print("=" * 40)
    print()
    print("1. 🔍 Memory Scanner")
    print("2. 🎮 Launch Bot") 
    print("3. ❓ Help")
    print()
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        subprocess.run([sys.executable, "memory_scanner.py"])
    elif choice == "2":
        subprocess.run([sys.executable, "tantra_bot.py"])
    elif choice == "3":
        os.startfile("QUICK_START.md")
    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()
'''
        
        with open("launcher.py", "w") as f:
            f.write(launcher_content)
        
        self.log("✅ Launcher scripts created")
    
    def test_bot_functionality(self):
        """Test if the bot can run without errors"""
        self.log("🧪 Testing bot functionality...")
        
        try:
            # Test imports
            result = subprocess.run([
                sys.executable, "-c", 
                "import pymem, tkinter, pyautogui, keyboard; print('All imports successful')"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log("✅ All imports working correctly")
                return True
            else:
                self.log(f"❌ Import test failed: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Test failed: {e}", "ERROR")
            return False
    
    def run_complete_setup(self):
        """Run the complete automated setup"""
        self.log("🚀 Starting FULLY AUTOMATED Tantra Bot Setup", "SUCCESS")
        self.log("=" * 60)
        
        # Check admin privileges
        if not self.check_admin():
            self.log("⚠️ Not running as Administrator", "WARN")
            self.log("💡 For best results, run as Administrator")
        
        # Step 1: Python installation
        if not self.install_python_if_needed():
            self.log("❌ Setup failed at Python installation", "ERROR")
            return False
        
        # Step 2: Package installation  
        if not self.install_packages():
            self.log("❌ Setup failed at package installation", "ERROR")
            return False
        
        # Step 3: Create configurations
        self.create_default_config()
        
        # Step 4: Create launchers
        self.create_launcher_scripts()
        
        # Step 5: Test functionality
        if not self.test_bot_functionality():
            self.log("❌ Setup failed at functionality test", "ERROR")
            return False
        
        # Success!
        self.log("=" * 60)
        self.log("🎉 SETUP COMPLETE! Bot is ready to use!", "SUCCESS")
        self.log("=" * 60)
        
        self.show_next_steps()
        return True
    
    def show_next_steps(self):
        """Show what to do next"""
        self.log("📋 NEXT STEPS:", "SUCCESS")
        self.log("1. Start Tantra game and log in to your character")
        self.log("2. Run: python memory_scanner.py")
        self.log("3. Find your memory addresses (HP, TP, names)")
        self.log("4. Run: python tantra_bot.py")
        self.log("5. Configure and start botting!")
        self.log("")
        self.log("💡 TIP: Use launch_bot.bat for easy access")
        self.log("📖 Read QUICK_START.md for detailed instructions")

def main():
    """Main setup function"""
    print("\n🎮 TANTRA BOT - FULLY AUTOMATED SETUP")
    print("=" * 50)
    print("This will automatically:")
    print("✅ Install Python (if needed)")
    print("✅ Install all required packages")
    print("✅ Create configuration files")
    print("✅ Set up launcher scripts")
    print("✅ Test everything works")
    print("=" * 50)
    
    input("Press Enter to start automated setup...")
    
    setup = AutoSetup()
    success = setup.run_complete_setup()
    
    if success:
        print("\n🎉 SUCCESS! Your bot is ready!")
        print("Run 'python memory_scanner.py' next to find addresses.")
    else:
        print("\n❌ Setup failed. Check the error messages above.")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
