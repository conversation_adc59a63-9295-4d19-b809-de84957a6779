#!/usr/bin/env python3
"""
AUTOMATIC Address Finder for Tantra
Finds memory addresses automatically without user input
"""

import pymem
import pymem.process
import time
import json
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import psutil
import struct
from typing import List, Dict, Optional, Tuple

class AutoAddressFinder:
    """Automatically finds memory addresses for Tantra"""
    
    def __init__(self):
        self.process = None
        self.found_addresses = {}
        self.monitoring = False
        
    def find_kathana_process(self) -> bool:
        """Automatically find and attach to Kathana process"""
        print("🔍 Searching for Kathana process...")

        # Common Kathana process names
        possible_names = [
            "<PERSON>hana", "kathana", "Kathana.exe", "kathana.exe",
            "Kathana - The Coming of the Dark Ages",
            "client", "client.exe", "game", "game.exe",
            "KathanaClient", "KathanaGame",
            "Tantra", "tantra", "Tantra.exe", "tantra.exe"  # Keep as backup
        ]
        
        # Search running processes
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_name = proc.info['name']
                if proc_name:
                    for target in possible_names:
                        if target.lower() in proc_name.lower():
                            print(f"✅ Found potential Kathana process: {proc_name} (PID: {proc.info['pid']})")

                            # Try to attach
                            try:
                                self.process = pymem.Pymem(proc.info['pid'])
                                print(f"✅ Successfully attached to {proc_name}")
                                return True
                            except Exception as e:
                                print(f"❌ Failed to attach to {proc_name}: {e}")
                                continue
            except:
                continue
        
        print("❌ No Kathana process found")
        return False
    
    def scan_memory_range(self, start_addr: int, end_addr: int, value: int, value_type: str = "int") -> List[int]:
        """Scan a specific memory range for a value"""
        addresses = []
        
        try:
            # Read memory in chunks
            chunk_size = 0x1000  # 4KB chunks
            current_addr = start_addr
            
            while current_addr < end_addr:
                try:
                    # Calculate chunk size
                    remaining = end_addr - current_addr
                    read_size = min(chunk_size, remaining)
                    
                    # Read memory chunk
                    data = self.process.read_bytes(current_addr, read_size)
                    
                    # Search for value in chunk
                    if value_type == "int":
                        pattern = struct.pack('<I', value)
                        step = 4
                    elif value_type == "short":
                        pattern = struct.pack('<H', value)
                        step = 2
                    else:
                        pattern = struct.pack('B', value)
                        step = 1
                    
                    # Find all occurrences in this chunk
                    offset = 0
                    while True:
                        pos = data.find(pattern, offset)
                        if pos == -1:
                            break
                        
                        addr = current_addr + pos
                        addresses.append(addr)
                        offset = pos + step
                        
                        # Limit results
                        if len(addresses) > 1000:
                            return addresses
                    
                    current_addr += read_size
                    
                except:
                    current_addr += chunk_size
                    continue
        
        except Exception as e:
            print(f"❌ Memory scan error: {e}")
        
        return addresses
    
    def find_character_hp_auto(self) -> Optional[int]:
        """Automatically find character HP address"""
        print("🔍 Auto-finding Character HP address...")
        
        # Strategy: Look for values in typical HP ranges
        hp_candidates = []
        
        # Common HP ranges to scan for
        hp_ranges = [
            (100, 500),    # Low level characters
            (500, 2000),   # Mid level characters  
            (2000, 10000), # High level characters
            (10000, 50000) # Very high level characters
        ]
        
        for min_hp, max_hp in hp_ranges:
            print(f"Scanning for HP values {min_hp}-{max_hp}...")
            
            for hp_value in range(min_hp, max_hp, 50):  # Check every 50 HP
                addresses = self.scan_for_value(hp_value)
                
                if 1 <= len(addresses) <= 10:  # Reasonable number of matches
                    hp_candidates.extend(addresses)
                    
                if len(hp_candidates) > 100:
                    break
        
        if hp_candidates:
            # Return the first candidate in a reasonable memory range
            for addr in hp_candidates:
                if 0x400000 <= addr <= 0x7FFFFFFF:  # Typical game memory range
                    print(f"✅ Found potential HP address: {hex(addr)}")
                    return addr
        
        print("❌ Could not auto-find HP address")
        return None
    
    def find_character_name_auto(self) -> Optional[int]:
        """Automatically find character name address"""
        print("🔍 Auto-finding Character Name address...")
        
        # Strategy: Look for common character name patterns
        common_names = [
            "Player", "Char", "Test", "Admin", "GM",
            "Knight", "Mage", "Archer", "Warrior"
        ]
        
        for name in common_names:
            addresses = self.scan_for_string(name)
            if addresses:
                for addr in addresses:
                    if 0x400000 <= addr <= 0x7FFFFFFF:
                        print(f"✅ Found potential name address: {hex(addr)} ('{name}')")
                        return addr
        
        print("❌ Could not auto-find character name address")
        return None
    
    def scan_for_value(self, value: int, value_type: str = "int") -> List[int]:
        """Scan entire process memory for a value"""
        addresses = []
        
        if not self.process:
            return addresses
        
        try:
            # Get memory regions
            for region in self.process.memory_regions():
                # Only scan committed, readable regions
                if region.State != 0x1000:  # MEM_COMMIT
                    continue
                if region.Protect not in [0x04, 0x20, 0x40]:  # PAGE_READONLY, PAGE_EXECUTE_READ, PAGE_READWRITE
                    continue
                
                # Skip very large regions to avoid timeouts
                if region.RegionSize > 0x1000000:  # Skip regions larger than 16MB
                    continue
                
                try:
                    region_addresses = self.scan_memory_range(
                        region.BaseAddress, 
                        region.BaseAddress + region.RegionSize,
                        value, 
                        value_type
                    )
                    addresses.extend(region_addresses)
                    
                    # Limit total results
                    if len(addresses) > 5000:
                        break
                        
                except:
                    continue
        
        except Exception as e:
            print(f"❌ Full scan error: {e}")
        
        return addresses
    
    def scan_for_string(self, text: str) -> List[int]:
        """Scan memory for a text string"""
        addresses = []
        
        if not self.process:
            return addresses
        
        try:
            # Try different encodings
            patterns = [
                text.encode('ascii') + b'\x00',
                text.encode('utf-8') + b'\x00',
                text.encode('utf-16le') + b'\x00\x00'
            ]
            
            for region in self.process.memory_regions():
                if region.State != 0x1000:
                    continue
                if region.Protect not in [0x04, 0x20, 0x40]:
                    continue
                if region.RegionSize > 0x1000000:
                    continue
                
                try:
                    data = self.process.read_bytes(region.BaseAddress, region.RegionSize)
                    
                    for pattern in patterns:
                        offset = 0
                        while True:
                            pos = data.find(pattern, offset)
                            if pos == -1:
                                break
                            
                            addr = region.BaseAddress + pos
                            addresses.append(addr)
                            offset = pos + 1
                            
                            if len(addresses) > 100:
                                return addresses
                                
                except:
                    continue
        
        except Exception as e:
            print(f"❌ String scan error: {e}")
        
        return addresses
    
    def auto_find_all_addresses(self) -> Dict[str, int]:
        """Automatically find all important addresses"""
        print("🚀 Starting automatic address discovery...")
        
        if not self.find_kathana_process():
            return {}
        
        addresses = {}
        
        # Find character HP
        hp_addr = self.find_character_hp_auto()
        if hp_addr:
            addresses["character_hp"] = hp_addr
            
            # Try to find related addresses nearby
            addresses["character_max_hp"] = hp_addr + 4
            addresses["character_tp"] = hp_addr + 8
            addresses["character_max_tp"] = hp_addr + 12
        
        # Find character name
        name_addr = self.find_character_name_auto()
        if name_addr:
            addresses["character_name"] = name_addr
        
        # Try to find target-related addresses
        # These are usually in a different memory region
        target_hp_candidates = self.scan_for_value(100)  # Look for low HP values (monsters)
        if target_hp_candidates:
            addresses["target_hp"] = target_hp_candidates[0]
            addresses["target_name"] = target_hp_candidates[0] - 0x50  # Name usually before HP
        
        return addresses
    
    def save_addresses(self, addresses: Dict[str, int], filename: str = "addresses.json"):
        """Save found addresses to file"""
        try:
            # Convert to hex strings for JSON
            hex_addresses = {name: hex(addr) for name, addr in addresses.items()}
            
            with open(filename, 'w') as f:
                json.dump(hex_addresses, f, indent=2)
            
            print(f"✅ Addresses saved to {filename}")
            return True
        except Exception as e:
            print(f"❌ Failed to save addresses: {e}")
            return False
    
    def validate_addresses(self, addresses: Dict[str, int]) -> Dict[str, bool]:
        """Validate that addresses are readable"""
        results = {}
        
        for name, addr in addresses.items():
            try:
                if "name" in name.lower():
                    # Try to read as string
                    value = self.process.read_string(addr, 20)
                    results[name] = bool(value and len(value) > 0)
                else:
                    # Try to read as integer
                    value = self.process.read_int(addr)
                    results[name] = value is not None and value >= 0
            except:
                results[name] = False
        
        return results

class AutoFinderGUI:
    """GUI for automatic address finder"""
    
    def __init__(self):
        self.finder = AutoAddressFinder()
        self.setup_gui()
    
    def setup_gui(self):
        """Create the GUI"""
        self.root = tk.Tk()
        self.root.title("Automatic Address Finder")
        self.root.geometry("600x500")
        
        # Header
        header = tk.Label(self.root, text="🤖 Automatic Address Finder", 
                         font=('Arial', 16, 'bold'))
        header.pack(pady=10)
        
        # Instructions
        instructions = tk.Label(self.root,
                               text="This will automatically find memory addresses for Kathana.\n"
                                    "Make sure Kathana is running and you're logged in!",
                               justify='center')
        instructions.pack(pady=5)
        
        # Start button
        self.start_btn = tk.Button(self.root, text="🚀 Start Auto-Discovery", 
                                  command=self.start_discovery,
                                  font=('Arial', 12, 'bold'),
                                  bg='#4CAF50', fg='white')
        self.start_btn.pack(pady=20)
        
        # Progress bar
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(fill='x', padx=20, pady=5)
        
        # Results area
        results_frame = tk.LabelFrame(self.root, text="Discovery Results")
        results_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.results_text = tk.Text(results_frame, height=15, font=('Courier', 9))
        scrollbar = tk.Scrollbar(results_frame, orient='vertical', command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y', pady=5)
        
        # Status
        self.status_var = tk.StringVar(value="Ready - Click Start to begin")
        status_label = tk.Label(self.root, textvariable=self.status_var, relief='sunken')
        status_label.pack(side='bottom', fill='x')
    
    def log(self, message):
        """Add message to results"""
        self.results_text.insert(tk.END, message + "\n")
        self.results_text.see(tk.END)
        self.root.update()
    
    def start_discovery(self):
        """Start the automatic discovery process"""
        self.start_btn.config(state='disabled')
        self.progress.start()
        self.status_var.set("Discovering addresses...")
        
        # Run in thread to keep GUI responsive
        thread = threading.Thread(target=self.run_discovery)
        thread.daemon = True
        thread.start()
    
    def run_discovery(self):
        """Run the discovery process"""
        try:
            self.log("🚀 Starting automatic address discovery...")
            self.log("=" * 50)
            
            # Find addresses
            addresses = self.finder.auto_find_all_addresses()
            
            if addresses:
                self.log(f"✅ Found {len(addresses)} addresses:")
                self.log("")
                
                for name, addr in addresses.items():
                    self.log(f"  {name}: {hex(addr)}")
                
                # Validate addresses
                self.log("")
                self.log("🧪 Validating addresses...")
                validation = self.finder.validate_addresses(addresses)
                
                valid_count = sum(validation.values())
                self.log(f"✅ {valid_count}/{len(addresses)} addresses are valid")
                
                # Save addresses
                if self.finder.save_addresses(addresses):
                    self.log("")
                    self.log("💾 Addresses saved to addresses.json")
                    self.log("🎉 Discovery complete! You can now use the bot.")
                    
                    self.status_var.set("Discovery complete - Addresses saved!")
                else:
                    self.log("❌ Failed to save addresses")
                    self.status_var.set("Discovery failed - Could not save")
            else:
                self.log("❌ No addresses found")
                self.log("💡 Make sure Kathana is running and you're logged in")
                self.status_var.set("Discovery failed - No addresses found")
            
        except Exception as e:
            self.log(f"❌ Discovery error: {e}")
            self.status_var.set("Discovery failed - Error occurred")
        
        finally:
            self.progress.stop()
            self.start_btn.config(state='normal')
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🤖 Automatic Address Finder for Kathana")
    print("=" * 40)
    
    # Check if GUI or command line
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--cli":
        # Command line mode
        finder = AutoAddressFinder()
        addresses = finder.auto_find_all_addresses()
        
        if addresses:
            print(f"✅ Found {len(addresses)} addresses")
            finder.save_addresses(addresses)
            print("🎉 Addresses saved to addresses.json")
        else:
            print("❌ No addresses found")
    else:
        # GUI mode
        gui = AutoFinderGUI()
        gui.run()

if __name__ == "__main__":
    main()
