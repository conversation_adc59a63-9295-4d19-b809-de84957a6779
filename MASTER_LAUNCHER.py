#!/usr/bin/env python3
"""
🎮 TANTRA BOT - MASTER LAUNCHER 🎮
DOES EVERYTHING AUTOMATICALLY - NO USER INPUT NEEDED!

This script will:
1. Install Python if needed
2. Install all packages
3. Find memory addresses automatically  
4. Launch the bot ready to use
"""

import subprocess
import sys
import os
import time
import json
import platform
from pathlib import Path

class MasterLauncher:
    def __init__(self):
        self.step = 0
        self.total_steps = 6
        
    def print_header(self):
        """Print fancy header"""
        print("\n" + "="*60)
        print("🎮 TANTRA BOT - MASTER LAUNCHER 🎮")
        print("FULLY AUTOMATED SETUP - NO USER INPUT NEEDED!")
        print("="*60)
        
    def print_step(self, message):
        """Print current step"""
        self.step += 1
        print(f"\n[STEP {self.step}/{self.total_steps}] {message}")
        print("-" * 50)
        
    def run_script(self, script_name, description):
        """Run a Python script and return success"""
        print(f"🔄 {description}...")
        
        try:
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print(f"✅ {description} completed successfully!")
                return True
            else:
                print(f"❌ {description} failed!")
                print(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} timed out!")
            return False
        except Exception as e:
            print(f"❌ Error running {script_name}: {e}")
            return False
    
    def check_tantra_running(self):
        """Check if Tantra is running"""
        try:
            import psutil
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and 'tantra' in proc.info['name'].lower():
                    return True
            return False
        except:
            return False
    
    def wait_for_tantra(self):
        """Wait for user to start Tantra"""
        print("⏳ Waiting for Tantra to start...")
        print("💡 Please start Tantra and log in to your character")
        print("   The script will automatically detect when it's ready")
        
        while True:
            if self.check_tantra_running():
                print("✅ Tantra detected! Waiting 10 seconds for full load...")
                time.sleep(10)
                return True
            
            print(".", end="", flush=True)
            time.sleep(2)
    
    def create_final_launcher(self):
        """Create the final easy launcher"""
        launcher_code = '''#!/usr/bin/env python3
"""
🎮 TANTRA BOT - EASY LAUNCHER
Everything is set up! Just run this to start botting.
"""

import subprocess
import sys
import os
import json

def main():
    print("🎮 TANTRA BOT - EASY LAUNCHER")
    print("=" * 40)
    
    # Check if addresses exist
    if not os.path.exists("addresses.json"):
        print("❌ No addresses found!")
        print("🔧 Running address finder...")
        subprocess.run([sys.executable, "auto_address_finder.py", "--cli"])
    
    # Check if addresses are valid
    try:
        with open("addresses.json", "r") as f:
            addresses = json.load(f)
        
        if len(addresses) < 3:
            print("⚠️ Not enough addresses found")
            print("🔧 Re-running address finder...")
            subprocess.run([sys.executable, "auto_address_finder.py", "--cli"])
    except:
        print("❌ Invalid addresses file")
        print("🔧 Running address finder...")
        subprocess.run([sys.executable, "auto_address_finder.py", "--cli"])
    
    # Launch the bot
    print("🚀 Launching Tantra Bot...")
    subprocess.run([sys.executable, "tantra_bot.py"])

if __name__ == "__main__":
    main()
'''
        
        with open("LAUNCH_BOT.py", "w") as f:
            f.write(launcher_code)
        
        # Also create a batch file for Windows
        batch_code = '''@echo off
title Tantra Bot - Easy Launcher
echo.
echo 🎮 TANTRA BOT - EASY LAUNCHER
echo ================================
echo.
python LAUNCH_BOT.py
pause
'''
        
        with open("LAUNCH_BOT.bat", "w") as f:
            f.write(batch_code)
        
        print("✅ Easy launchers created!")
        print("   - LAUNCH_BOT.py (Python)")
        print("   - LAUNCH_BOT.bat (Windows)")
    
    def run_complete_setup(self):
        """Run the complete automated setup"""
        self.print_header()
        
        print("This script will automatically:")
        print("✅ Install Python and packages")
        print("✅ Find memory addresses")
        print("✅ Set up the bot")
        print("✅ Create easy launchers")
        print("\nNo user input required!")
        
        input("\nPress Enter to start the magic... 🪄")
        
        # Step 1: Run auto setup
        self.print_step("Installing Python and packages")
        if not self.run_script("auto_setup.py", "Python and package installation"):
            print("❌ Setup failed! Please check the errors above.")
            return False
        
        # Step 2: Wait for Tantra
        self.print_step("Waiting for Tantra game")
        if not self.wait_for_tantra():
            print("❌ Tantra not detected!")
            return False
        
        # Step 3: Find addresses automatically
        self.print_step("Finding memory addresses automatically")
        if not self.run_script("auto_address_finder.py --cli", "Automatic address discovery"):
            print("❌ Address finding failed!")
            print("💡 You can try the manual memory scanner later")
        
        # Step 4: Verify addresses
        self.print_step("Verifying found addresses")
        try:
            with open("addresses.json", "r") as f:
                addresses = json.load(f)
            
            print(f"✅ Found {len(addresses)} addresses:")
            for name, addr in addresses.items():
                print(f"   {name}: {addr}")
                
        except Exception as e:
            print(f"⚠️ Could not verify addresses: {e}")
        
        # Step 5: Create easy launchers
        self.print_step("Creating easy launchers")
        self.create_final_launcher()
        
        # Step 6: Final success
        self.print_step("Setup complete!")
        
        print("\n" + "="*60)
        print("🎉 TANTRA BOT SETUP COMPLETE! 🎉")
        print("="*60)
        
        print("\n📋 WHAT'S READY:")
        print("✅ Python and all packages installed")
        print("✅ Memory addresses found and saved")
        print("✅ Bot configured and ready to use")
        print("✅ Easy launchers created")
        
        print("\n🚀 HOW TO USE:")
        print("1. Double-click 'LAUNCH_BOT.bat' (Windows)")
        print("2. Or run: python LAUNCH_BOT.py")
        print("3. Configure your settings in the bot GUI")
        print("4. Click 'Start Bot' and enjoy!")
        
        print("\n💡 TIPS:")
        print("- The bot will automatically use the found addresses")
        print("- Add your target monsters in the Main Controls tab")
        print("- Adjust skill delays in Skills & Buffs tab")
        print("- Use F10 for emergency stop anytime")
        
        print("\n🎮 READY TO BOT! Have fun! ✨")
        
        return True

def check_files_exist():
    """Check if required files exist"""
    required_files = [
        "auto_setup.py",
        "auto_address_finder.py", 
        "tantra_bot.py",
        "memory_scanner.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease make sure all bot files are in the same folder!")
        return False
    
    return True

def main():
    """Main function"""
    # Check if all files exist
    if not check_files_exist():
        input("Press Enter to exit...")
        return
    
    # Run the master setup
    launcher = MasterLauncher()
    success = launcher.run_complete_setup()
    
    if success:
        print("\n🎉 SUCCESS! Your bot is ready to use!")
        
        # Ask if they want to launch now
        choice = input("\nWould you like to launch the bot now? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            print("🚀 Launching bot...")
            subprocess.run([sys.executable, "tantra_bot.py"])
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        print("💡 You can try running individual scripts manually:")
        print("   - python auto_setup.py")
        print("   - python memory_scanner.py")
        print("   - python tantra_bot.py")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
